# PitchGuard

**Secure Pitch Evaluation with TEE and Zero-Knowledge Proofs**

PitchGuard is a minimal MVP that demonstrates encrypted pitch submission, TEE-enforced NLP scoring, and zero-knowledge proof verification. It protects pitch confidentiality while ensuring transparent and verifiable evaluation.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │  TEE Enclave    │
│   (Next.js)     │    │   (FastAPI)     │    │   (Docker)      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Pitch Upload  │◄──►│ • Submission    │◄──►│ • Decryption    │
│ • Client Crypto │    │ • Score Retrieval│   │ • NLP Scoring   │
│ • Score Display │    │ • Proof Verify  │    │ • ZK Proof Gen  │
│ • Proof Verify  │    │ • Key Management│    │ • Re-encryption │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔐 Security Features

- **Client-Side Encryption**: Pitches encrypted with AES-GCM before transmission
- **TEE Processing**: Secure enclave handles decryption and scoring
- **Zero-Knowledge Proofs**: Verify scoring correctness without revealing data
- **End-to-End Privacy**: Only founders can decrypt their evaluation results

## 🚀 Quick Start

### Prerequisites

- <PERSON><PERSON> & Docker Compose
- Node.js (v18+)
- Python (3.10+)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PitchGuard
   ```

2. **Run setup script**
   ```bash
   python scripts/setup.py
   ```
   This will:
   - Generate RSA key pairs
   - Set up ZK circuit (if circom is installed)
   - Create sample data

3. **Start services**
   ```bash
   docker-compose up --build
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## 📁 Project Structure

```
PitchGuard/
├── frontend/               # Next.js frontend application
│   ├── app/               # App router pages
│   ├── components/        # React components
│   └── lib/               # Utility libraries
├── backend/               # FastAPI backend service
│   └── main.py           # Main API endpoints
├── enclave/               # TEE container & processing
│   ├── enclave.py        # Enclave service
│   ├── model/            # RoBERTa model files
│   └── zk-circuit/       # ZK circuit implementation
├── docker/               # Docker configuration
├── scripts/              # Setup and utility scripts
├── keys/                 # Generated RSA keys
└── data/                 # Shared data volume
```

## 🔄 User Flow

### Founder Flow

1. **Upload Pitch**
   - Select pitch document (PDF/text)
   - Client generates AES key and encrypts pitch
   - AES key encrypted with TEE's public key
   - Submit encrypted payload

2. **Retrieve Scores**
   - Poll for results using pitch ID
   - Receive encrypted evaluation results
   - Decrypt with founder's private key
   - View detailed scores

3. **Verify Proof**
   - Extract ZK proof from results
   - Submit proof for verification
   - Confirm scoring integrity

### Judge Flow

1. **View Submissions**
   - Access list of submitted pitches
   - See encrypted status (cannot read content)
   - Verify proofs independently

## 🧮 Scoring Criteria

The NLP model evaluates pitches on four criteria (1-5 scale):

- **Narrative Clarity**: How well the pitch communicates the idea
- **Originality**: Uniqueness and innovation of the concept
- **Team Strength**: Quality and experience of the founding team
- **Market Fit**: Viability and market opportunity assessment

**Overall Score**: Average of the four criteria

## 🔬 Zero-Knowledge Circuit

The ZK circuit (`pitch_eval.circom`) proves:

1. **Score Validity**: Each score is in range [1,5]
2. **Computation Correctness**: Scores derived from pitch hash
3. **Model Integrity**: Consistent model commitment
4. **Data Binding**: Proof linked to specific pitch

**Public Signals**:
- Pitch hash (proves knowledge of original pitch)
- Model commitment (ensures consistent scoring model)

## 🛠️ Development

### Manual Setup (Alternative)

If the setup script fails, you can set up manually:

1. **Generate Keys**
   ```bash
   python scripts/generate_keys.py
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.enclave.txt
   pip install -r requirements.backend.txt
   ```

3. **Set up ZK Circuit** (requires circom)
   ```bash
   cd enclave/zk-circuit
   chmod +x setup_circuit.sh
   ./setup_circuit.sh
   ```

### Testing

1. **Test Backend**
   ```bash
   curl http://localhost:8000/health
   ```

2. **Test Enclave**
   ```bash
   curl http://localhost:5001/health
   ```

3. **Submit Sample Pitch**
   Use the sample pitch in `data/sample_pitch.txt` through the frontend

## 🐳 Docker Services

- **enclave**: TEE container with NLP model and ZK circuit
- **backend**: FastAPI service handling API requests
- **frontend**: Next.js web application (if built)

### Service Health Checks

All services include health checks accessible at `/health` endpoints.

## 🔧 Configuration

### Environment Variables

**Backend**:
- `ENCLAVE_URL`: URL of the enclave service (default: http://enclave:5001)

**Frontend**:
- `NEXT_PUBLIC_BACKEND_URL`: Backend API URL
- `NEXT_PUBLIC_TEE_PUBKEY_URL`: TEE public key endpoint

### File Paths

- TEE Private Key: `/app/tee_private_key.pem`
- Verification Key: `/app/zk-circuit/verification_key.json`
- Data Storage: `/data/`

## 🚨 Security Considerations

### MVP Limitations

- **Mock ZK Proofs**: For demo purposes, simplified proof generation
- **Simplified Model**: Basic RoBERTa without extensive fine-tuning
- **Development Keys**: Not for production use
- **Local Storage**: No persistent database

### Production Recommendations

- Use hardware-based TEE (Intel SGX, ARM TrustZone)
- Implement proper key management (HSM, key rotation)
- Add comprehensive audit logging
- Use production-grade ZK proving systems
- Implement rate limiting and DDoS protection

## 📊 Demo Scenarios

### Scenario 1: Successful Evaluation
1. Upload sample pitch
2. Wait for processing (30-60 seconds)
3. Decrypt and view scores
4. Verify ZK proof

### Scenario 2: Privacy Verification
1. Submit pitch as Founder A
2. Try to access as Judge (should see encrypted data only)
3. Verify proof works without revealing content

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Circom**: Zero-knowledge circuit framework
- **snarkjs**: JavaScript ZK-SNARK library
- **Transformers**: Hugging Face NLP library
- **FastAPI**: Modern Python web framework
- **Next.js**: React framework for production

## 📞 Support

For questions or issues:
1. Check the troubleshooting section below
2. Review Docker logs: `docker-compose logs`
3. Open an issue on GitHub

## 🔍 Troubleshooting

### Common Issues

**Port conflicts**:
```bash
# Check what's using the ports
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000
netstat -tulpn | grep :5001
```

**Docker build failures**:
```bash
# Clean Docker cache
docker system prune -a
docker-compose build --no-cache
```

**ZK circuit setup fails**:
- Install circom: https://docs.circom.io/getting-started/installation/
- Install snarkjs: `npm install -g snarkjs`
- Run setup manually: `cd enclave/zk-circuit && ./setup_circuit.sh`

**Key generation fails**:
```bash
# Install cryptography
pip install cryptography
python scripts/generate_keys.py
```
