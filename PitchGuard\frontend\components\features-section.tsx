"use client"

import { useRef, useEffect, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Center } from "@react-three/drei"
import { useFrame } from "@react-three/fiber"
import { Lock, Server, CheckCircle } from "lucide-react"
import type * as THREE from "three"

function EncryptionAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.rotation.y = time * 0.5
    }
  })

  return (
    <group ref={groupRef}>
      <mesh>
        <boxGeometry args={[1, 1.2, 0.2]} />
        <meshStandardMaterial color="#00FFD1" transparent opacity={0.7} />
      </mesh>
      <mesh position={[0, 0, 0.2]}>
        <torusGeometry args={[0.3, 0.1, 8, 16]} />
        <meshStandardMaterial color="#A3A3FF" />
      </mesh>
    </group>
  )
}

function ServerAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.children.forEach((child, i) => {
        child.position.y = Math.sin(time + i) * 0.1
      })
    }
  })

  return (
    <group ref={groupRef}>
      {Array.from({ length: 3 }).map((_, i) => (
        <mesh key={i} position={[0, i * 0.4 - 0.4, 0]}>
          <boxGeometry args={[1.5, 0.3, 0.8]} />
          <meshStandardMaterial color="#00FFD1" transparent opacity={0.6} />
        </mesh>
      ))}
      {/* Glowing nodes */}
      {Array.from({ length: 6 }).map((_, i) => (
        <mesh key={i} position={[((i % 3) - 1) * 0.4, Math.floor(i / 3) * 0.4 - 0.2, 0.5]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial color="#A3A3FF" emissive="#A3A3FF" emissiveIntensity={0.5} />
        </mesh>
      ))}
    </group>
  )
}

function ProofAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.rotation.z = Math.sin(time) * 0.2
    }
  })

  return (
    <group ref={groupRef}>
      <mesh>
        <octahedronGeometry args={[0.8]} />
        <meshStandardMaterial color="#00FFD1" wireframe transparent opacity={0.8} />
      </mesh>
      <mesh>
        <sphereGeometry args={[0.3]} />
        <meshStandardMaterial color="#A3A3FF" emissive="#A3A3FF" emissiveIntensity={0.3} />
      </mesh>
    </group>
  )
}

function FeaturePanel({
  title,
  description,
  icon: Icon,
  animation: Animation,
  isVisible,
}: {
  title: string
  description: string
  icon: any
  animation: any
  isVisible: boolean
}) {
  return (
    <div
      className={`grid md:grid-cols-2 gap-8 items-center transition-all duration-1000 ${
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-20"
      }`}
    >
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Icon className="w-8 h-8 text-cyan-400" />
          <h3 className="text-3xl font-bold font-orbitron text-white">{title}</h3>
        </div>
        <p className="text-lg text-gray-300 leading-relaxed">{description}</p>
      </div>

      <div className="h-64 md:h-80">
        <Canvas camera={{ position: [0, 0, 3] }}>
          <Environment preset="night" />
          <ambientLight intensity={0.5} />
          <pointLight position={[5, 5, 5]} intensity={1} color="#00FFD1" />
          <Center>
            <Animation isVisible={isVisible} />
          </Center>
        </Canvas>
      </div>
    </div>
  )
}

export default function FeaturesSection() {
  const [visiblePanels, setVisiblePanels] = useState<boolean[]>([false, false, false])
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            setVisiblePanels((prev) => {
              const newState = [...prev]
              newState[index] = true
              return newState
            })
          }
        })
      },
      { threshold: 0.3 },
    )

    // Add null check for sectionRef.current
    if (sectionRef.current) {
      const panels = sectionRef.current.querySelectorAll(".feature-panel")
      panels?.forEach((panel) => observer.observe(panel))
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  const features = [
    {
      title: "Seamless Encryption",
      description:
        "Your pitch decks are encrypted end-to-end before any analysis begins. Our advanced encryption protocols ensure your intellectual property remains completely secure throughout the evaluation process.",
      icon: Lock,
      animation: EncryptionAnimation,
    },
    {
      title: "AI Scoring in Secure Enclave",
      description:
        "Our AI models operate within secure enclaves, processing your data in isolated environments. This ensures that even our systems cannot access your raw pitch content while providing accurate scoring.",
      icon: Server,
      animation: ServerAnimation,
    },
    {
      title: "Zero-Knowledge Proof",
      description:
        "Receive comprehensive evaluation results without exposing your pitch content. Our zero-knowledge proof system validates the analysis while maintaining complete privacy of your business information.",
      icon: CheckCircle,
      animation: ProofAnimation,
    },
  ]

  return (
    <section id="features" ref={sectionRef} className="py-20 px-6 bg-gradient-to-b from-indigo-950 to-slate-900">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold font-orbitron text-white mb-6">Revolutionary Features</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience the future of pitch evaluation with our cutting-edge privacy-first technology
          </p>
        </div>

        <div className="space-y-32">
          {features.map((feature, index) => (
            <div key={index} className="feature-panel">
              <FeaturePanel
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                animation={feature.animation}
                isVisible={visiblePanels[index]}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
