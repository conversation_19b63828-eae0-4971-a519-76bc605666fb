.card-swap-container {
  position: relative;
  perspective: 1000px;
  transform-style: preserve-3d;
  overflow: hidden;
}

.card-swap-container .card {
  position: absolute;
  top: 50%;
  left: 50%;
  cursor: pointer;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  transition: box-shadow 0.3s ease;
  will-change: transform;
  backface-visibility: hidden;
}

.card-swap-container .card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.card-swap-container .card.feature-card {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  padding: 20px;
  text-align: center;
}

.card-swap-container .card.security-card {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-swap-container .card.tech-card {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-swap-container .card.privacy-card {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.card-swap-container .card.innovation-card {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Card content styling */
.card-content {
  padding: 24px;
  text-align: center;
}

.card-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  color: white;
}

.card-content p {
  font-size: 0.95rem;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 16px;
}

.card-content .card-icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
  display: block;
}

.card-content .card-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.card-content .stat {
  text-align: center;
}

.card-content .stat-number {
  font-size: 1.8rem;
  font-weight: 800;
  display: block;
}

.card-content .stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
