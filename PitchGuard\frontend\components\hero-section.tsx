"use client"

import { useRef } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Center } from "@react-three/drei"
import type * as THREE from "three"

function AnimatedLock() {
  const lockRef = useRef<THREE.Group>(null)
  const deckRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    const time = state.clock.getElapsedTime()

    if (lockRef.current) {
      lockRef.current.rotation.y = time * 0.5
      lockRef.current.position.y = Math.sin(time) * 0.1
    }

    if (deckRef.current) {
      deckRef.current.rotation.x = Math.sin(time * 0.5) * 0.1
      deckRef.current.rotation.z = Math.cos(time * 0.3) * 0.05
    }
  })

  return (
    <group>
      {/* Lock wireframe */}
      <group ref={lockRef} position={[-2, 0, 0]}>
        <mesh>
          <boxGeometry args={[1, 1.5, 0.3]} />
          <meshBasicMaterial color="#00FFD1" wireframe transparent opacity={0.6} />
        </mesh>
        <mesh position={[0, 0.5, 0]}>
          <torusGeometry args={[0.4, 0.1, 8, 16]} />
          <meshBasicMaterial color="#00FFD1" wireframe transparent opacity={0.8} />
        </mesh>
      </group>

      {/* Morphing connection */}
      <group position={[0, 0, 0]}>
        <mesh>
          <cylinderGeometry args={[0.05, 0.05, 2, 8]} />
          <meshBasicMaterial color="#A3A3FF" transparent opacity={0.4} />
        </mesh>
      </group>

      {/* Pitch deck wireframe */}
      <group ref={deckRef} position={[2, 0, 0]}>
        <mesh>
          <boxGeometry args={[1.2, 0.8, 0.1]} />
          <meshBasicMaterial color="#00FFD1" wireframe transparent opacity={0.6} />
        </mesh>
        <mesh position={[0, 0, 0.1]}>
          <planeGeometry args={[1, 0.6]} />
          <meshBasicMaterial color="#A3A3FF" transparent opacity={0.3} />
        </mesh>
      </group>

      {/* Floating particles */}
      {Array.from({ length: 20 }).map((_, i) => (
        <mesh key={i} position={[(Math.random() - 0.5) * 10, (Math.random() - 0.5) * 6, (Math.random() - 0.5) * 4]}>
          <sphereGeometry args={[0.02]} />
          <meshBasicMaterial color="#00FFD1" transparent opacity={0.4} />
        </mesh>
      ))}
    </group>
  )
}

export default function HeroSection() {
  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={1} color="#00FFD1" />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#A3A3FF" />

      <Center>
        <AnimatedLock />
      </Center>
    </>
  )
}
