#!/bin/bash

# PitchGuard ZK Circuit Setup Script
# Compiles the Circom circuit and generates proving/verification keys

set -e

echo "Setting up PitchGuard ZK Circuit..."

# Check if circom is installed
if ! command -v circom &> /dev/null; then
    echo "Error: circom not found. Please install circom first."
    echo "Visit: https://docs.circom.io/getting-started/installation/"
    exit 1
fi

# Check if snarkjs is installed
if ! command -v snarkjs &> /dev/null; then
    echo "Error: snarkjs not found. Installing via npm..."
    npm install -g snarkjs
fi

CIRCUIT_DIR="$(dirname "$0")"
cd "$CIRCUIT_DIR"

echo "Compiling circuit..."
circom pitch_eval.circom --r1cs --wasm --sym

echo "Generating witness..."
cd pitch_eval_js
node generate_witness.js pitch_eval.wasm ../input.json witness.wtns
cd ..

echo "Starting trusted setup (Powers of Tau)..."
snarkjs powersoftau new bn128 12 pot12_0000.ptau -v

echo "Contributing to ceremony..."
snarkjs powersoftau contribute pot12_0000.ptau pot12_0001.ptau --name="First contribution" -v

echo "Preparing phase 2..."
snarkjs powersoftau prepare phase2 pot12_0001.ptau pot12_final.ptau -v

echo "Generating zkey..."
snarkjs groth16 setup pitch_eval.r1cs pot12_final.ptau pitch_eval_0000.zkey

echo "Contributing to phase 2..."
snarkjs zkey contribute pitch_eval_0000.zkey pitch_eval_0001.zkey --name="First contribution" -v

echo "Exporting verification key..."
snarkjs zkey export verificationkey pitch_eval_0001.zkey verification_key.json

echo "Creating sample input..."
cat > input.json << EOF
{
    "pitch_hash": "12345",
    "narrative_clarity": "3",
    "originality": "3", 
    "team_strength": "3",
    "market_fit": "3",
    "overall_score": "3",
    "model_commitment": "67890"
}
EOF

echo "Testing proof generation..."
cd pitch_eval_js
node generate_witness.js pitch_eval.wasm ../input.json witness.wtns
cd ..

snarkjs groth16 prove pitch_eval_0001.zkey pitch_eval_js/witness.wtns proof.json public.json

echo "Testing proof verification..."
snarkjs groth16 verify verification_key.json public.json proof.json

echo "Circuit setup complete!"
echo "Files generated:"
echo "- pitch_eval.r1cs (R1CS constraint system)"
echo "- pitch_eval_js/ (WASM witness generator)"
echo "- pitch_eval_0001.zkey (Proving key)"
echo "- verification_key.json (Verification key)"
echo "- proof.json (Sample proof)"
echo "- public.json (Sample public signals)"
