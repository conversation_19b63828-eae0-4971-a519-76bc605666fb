#!/usr/bin/env python3
"""
PitchGuard TEE Enclave
Handles encrypted pitch decryption, NLP scoring, and ZK proof generation
"""

import os
import json
import hashlib
import base64
import subprocess
import tempfile
from typing import Dict, Any, Tuple
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel
import uvicorn

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch
import numpy as np

app = FastAPI(title="PitchGuard TEE Enclave")

# Load TEE private key
TEE_PRIVATE_KEY_PATH = "/app/tee_private_key.pem"
MODEL_PATH = "/app/model"
ZK_CIRCUIT_PATH = "/app/zk-circuit"

class EncryptedPitchRequest(BaseModel):
    encrypted_aes_key: str
    iv: str
    tag: str
    encrypted_pitch: str
    founder_public_key: str

class EnclaveService:
    def __init__(self):
        self.tee_private_key = self._load_tee_private_key()
        self.tokenizer, self.model = self._load_nlp_model()
        
    def _load_tee_private_key(self):
        """Load TEE private key from PEM file"""
        try:
            with open(TEE_PRIVATE_KEY_PATH, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            return private_key
        except Exception as e:
            print(f"Error loading TEE private key: {e}")
            raise
    
    def _load_nlp_model(self):
        """Load fine-tuned RoBERTa model"""
        try:
            # For MVP, use a simple pre-trained model
            # In production, this would be a fine-tuned model
            tokenizer = AutoTokenizer.from_pretrained("roberta-base")
            model = AutoModelForSequenceClassification.from_pretrained("roberta-base")
            return tokenizer, model
        except Exception as e:
            print(f"Error loading NLP model: {e}")
            # Fallback to mock scoring
            return None, None
    
    def decrypt_aes_key(self, encrypted_aes_key: str) -> bytes:
        """Decrypt AES key using TEE private key"""
        encrypted_key_bytes = base64.b64decode(encrypted_aes_key)
        
        aes_key = self.tee_private_key.decrypt(
            encrypted_key_bytes,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return aes_key
    
    def decrypt_pitch(self, encrypted_pitch: str, iv: str, tag: str, aes_key: bytes) -> str:
        """Decrypt pitch using AES-GCM"""
        encrypted_data = base64.b64decode(encrypted_pitch)
        iv_bytes = base64.b64decode(iv)
        tag_bytes = base64.b64decode(tag)
        
        cipher = Cipher(
            algorithms.AES(aes_key),
            modes.GCM(iv_bytes, tag_bytes),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        
        plaintext = decryptor.finalize_with_tag(encrypted_data, tag_bytes)
        return plaintext.decode('utf-8')
    
    def compute_scores(self, pitch_text: str) -> Dict[str, int]:
        """Compute pitch scores using NLP model"""
        if self.model is None or self.tokenizer is None:
            # Mock scoring for MVP
            pitch_hash = int(hashlib.sha256(pitch_text.encode()).hexdigest()[:8], 16)
            base_score = (pitch_hash % 5) + 1
            
            return {
                "narrative_clarity": base_score,
                "originality": base_score,
                "team_strength": base_score,
                "market_fit": base_score,
                "overall": base_score
            }
        
        # Real NLP scoring (simplified for MVP)
        try:
            inputs = self.tokenizer(pitch_text, return_tensors="pt", truncation=True, max_length=512)
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                
            # Convert logits to scores (simplified)
            scores_raw = torch.softmax(logits, dim=-1).numpy()[0]
            
            # Map to 1-5 scale (mock implementation)
            narrative_clarity = int((scores_raw[0] * 4) + 1)
            originality = int((scores_raw[1] * 4) + 1) if len(scores_raw) > 1 else narrative_clarity
            team_strength = int((scores_raw[2] * 4) + 1) if len(scores_raw) > 2 else narrative_clarity
            market_fit = int((scores_raw[3] * 4) + 1) if len(scores_raw) > 3 else narrative_clarity
            
            overall = (narrative_clarity + originality + team_strength + market_fit) // 4
            
            return {
                "narrative_clarity": narrative_clarity,
                "originality": originality,
                "team_strength": team_strength,
                "market_fit": market_fit,
                "overall": overall
            }
        except Exception as e:
            print(f"Error in NLP scoring: {e}")
            # Fallback to mock scoring
            return self.compute_scores(pitch_text)
    
    def generate_zk_proof(self, pitch_text: str, scores: Dict[str, int]) -> Dict[str, Any]:
        """Generate ZK proof for the scores"""
        try:
            # Compute pitch hash
            pitch_hash = int(hashlib.sha256(pitch_text.encode()).hexdigest()[:8], 16)
            model_commitment = int(hashlib.sha256(b"roberta-base-v1").hexdigest()[:8], 16)
            
            # Create input for ZK circuit
            circuit_input = {
                "pitch_hash": str(pitch_hash),
                "narrative_clarity": str(scores["narrative_clarity"]),
                "originality": str(scores["originality"]),
                "team_strength": str(scores["team_strength"]),
                "market_fit": str(scores["market_fit"]),
                "overall_score": str(scores["overall"]),
                "model_commitment": str(model_commitment)
            }
            
            # Generate proof using snarkjs
            with tempfile.TemporaryDirectory() as temp_dir:
                input_file = os.path.join(temp_dir, "input.json")
                witness_file = os.path.join(temp_dir, "witness.wtns")
                proof_file = os.path.join(temp_dir, "proof.json")
                public_file = os.path.join(temp_dir, "public.json")
                
                # Write input
                with open(input_file, 'w') as f:
                    json.dump(circuit_input, f)
                
                # Generate witness
                witness_cmd = [
                    "node", 
                    f"{ZK_CIRCUIT_PATH}/pitch_eval_js/generate_witness.js",
                    f"{ZK_CIRCUIT_PATH}/pitch_eval_js/pitch_eval.wasm",
                    input_file,
                    witness_file
                ]
                subprocess.run(witness_cmd, check=True, capture_output=True)
                
                # Generate proof
                proof_cmd = [
                    "snarkjs", "groth16", "prove",
                    f"{ZK_CIRCUIT_PATH}/pitch_eval_0001.zkey",
                    witness_file,
                    proof_file,
                    public_file
                ]
                subprocess.run(proof_cmd, check=True, capture_output=True)
                
                # Read proof and public signals
                with open(proof_file, 'r') as f:
                    proof = json.load(f)
                
                with open(public_file, 'r') as f:
                    public_signals = json.load(f)
                
                return {
                    "proof": proof,
                    "public_signals": public_signals,
                    "model_commitment": model_commitment,
                    "pitch_hash": pitch_hash
                }
                
        except Exception as e:
            print(f"Error generating ZK proof: {e}")
            # Return mock proof for MVP
            return {
                "proof": {"mock": True},
                "public_signals": [str(pitch_hash), str(model_commitment)],
                "model_commitment": model_commitment,
                "pitch_hash": pitch_hash
            }
    
    def encrypt_result(self, result: Dict[str, Any], founder_public_key_pem: str) -> str:
        """Encrypt result for founder using their public key"""
        # Load founder's public key
        founder_public_key = serialization.load_pem_public_key(
            founder_public_key_pem.encode(),
            backend=default_backend()
        )
        
        # Serialize result
        result_json = json.dumps(result).encode('utf-8')
        
        # Encrypt with founder's public key
        encrypted_result = founder_public_key.encrypt(
            result_json,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return base64.b64encode(encrypted_result).decode('utf-8')

# Global enclave service instance
enclave_service = EnclaveService()

@app.post("/process-pitch")
async def process_pitch(request: EncryptedPitchRequest):
    """Process encrypted pitch and return encrypted results"""
    try:
        # Decrypt AES key
        aes_key = enclave_service.decrypt_aes_key(request.encrypted_aes_key)
        
        # Decrypt pitch
        pitch_text = enclave_service.decrypt_pitch(
            request.encrypted_pitch,
            request.iv,
            request.tag,
            aes_key
        )
        
        print(f"Decrypted pitch: {pitch_text[:100]}...")
        
        # Compute scores
        scores = enclave_service.compute_scores(pitch_text)
        print(f"Computed scores: {scores}")
        
        # Generate ZK proof
        zk_proof_data = enclave_service.generate_zk_proof(pitch_text, scores)
        print("Generated ZK proof")
        
        # Prepare result
        result = {
            "scores": scores,
            "zk_proof": zk_proof_data["proof"],
            "public_signals": zk_proof_data["public_signals"],
            "model_commitment": zk_proof_data["model_commitment"]
        }
        
        # Encrypt result for founder
        encrypted_result = enclave_service.encrypt_result(result, request.founder_public_key)
        
        return {"encrypted_result": encrypted_result}
        
    except Exception as e:
        print(f"Error processing pitch: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "PitchGuard TEE Enclave"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5001)
