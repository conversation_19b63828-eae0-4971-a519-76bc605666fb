# PitchGuard Makefile
# Provides convenient commands for development and deployment

.PHONY: help setup build up down logs test clean keys model circuit

# Default target
help:
	@echo "PitchGuard Development Commands"
	@echo "==============================="
	@echo ""
	@echo "Setup & Installation:"
	@echo "  setup     - Run initial project setup"
	@echo "  keys      - Generate RSA key pairs"
	@echo "  model     - Set up NLP model"
	@echo "  circuit   - Set up ZK circuit"
	@echo ""
	@echo "Docker Operations:"
	@echo "  build     - Build Docker images"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  restart   - Restart all services"
	@echo "  logs      - View service logs"
	@echo ""
	@echo "Testing & Validation:"
	@echo "  test      - Run system tests"
	@echo "  health    - Check service health"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean     - Clean up containers and volumes"
	@echo "  reset     - Full reset (clean + rebuild)"
	@echo ""

# Setup and installation
setup:
	@echo "Running PitchGuard setup..."
	python scripts/setup.py

keys:
	@echo "Generating RSA key pairs..."
	python scripts/generate_keys.py

model:
	@echo "Setting up NLP model..."
	python enclave/model/setup_model.py

circuit:
	@echo "Setting up ZK circuit..."
	cd enclave/zk-circuit && chmod +x setup_circuit.sh && ./setup_circuit.sh

# Docker operations
build:
	@echo "Building Docker images..."
	docker-compose build

up:
	@echo "Starting PitchGuard services..."
	docker-compose up -d
	@echo "Services started. Access points:"
	@echo "  Frontend: http://localhost:3000"
	@echo "  Backend:  http://localhost:8000"
	@echo "  API Docs: http://localhost:8000/docs"

down:
	@echo "Stopping PitchGuard services..."
	docker-compose down

restart: down up

logs:
	@echo "Viewing service logs..."
	docker-compose logs -f

# Testing
test:
	@echo "Running system tests..."
	python scripts/test_system.py

health:
	@echo "Checking service health..."
	@curl -s http://localhost:8000/health | python -m json.tool || echo "Backend not responding"
	@curl -s http://localhost:5001/health | python -m json.tool || echo "Enclave not responding"

# Maintenance
clean:
	@echo "Cleaning up Docker resources..."
	docker-compose down -v
	docker system prune -f

reset: clean
	@echo "Full reset - rebuilding everything..."
	docker-compose build --no-cache
	docker-compose up -d

# Development helpers
dev-backend:
	@echo "Starting backend in development mode..."
	cd backend && uvicorn main:app --reload --host 0.0.0.0 --port 8000

dev-enclave:
	@echo "Starting enclave in development mode..."
	cd enclave && python enclave.py

dev-frontend:
	@echo "Starting frontend in development mode..."
	cd frontend && npm run dev

# Quick demo
demo:
	@echo "Running PitchGuard demo..."
	@echo "1. Starting services..."
	@make up
	@echo "2. Waiting for services to be ready..."
	@sleep 10
	@echo "3. Running system test..."
	@make test
	@echo "4. Demo complete! Check http://localhost:3000"

# Installation check
check-deps:
	@echo "Checking dependencies..."
	@command -v docker >/dev/null 2>&1 || { echo "Docker not found"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose not found"; exit 1; }
	@command -v python >/dev/null 2>&1 || { echo "Python not found"; exit 1; }
	@command -v node >/dev/null 2>&1 || { echo "Node.js not found"; exit 1; }
	@echo "✓ All dependencies found"

# Status check
status:
	@echo "PitchGuard Service Status"
	@echo "========================"
	@docker-compose ps

# View specific service logs
logs-backend:
	docker-compose logs -f backend

logs-enclave:
	docker-compose logs -f enclave

logs-frontend:
	docker-compose logs -f frontend

# Database/storage operations
backup-data:
	@echo "Backing up data directory..."
	@tar -czf pitchguard-data-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/

restore-data:
	@echo "Restore data from backup file:"
	@echo "  tar -xzf <backup-file> -C ."

# Security operations
rotate-keys:
	@echo "Rotating RSA keys..."
	@mv keys keys-backup-$(shell date +%Y%m%d-%H%M%S) 2>/dev/null || true
	@make keys
	@echo "Keys rotated. Old keys backed up."

# Monitoring
monitor:
	@echo "Monitoring PitchGuard services..."
	@watch -n 5 'docker-compose ps && echo "" && curl -s http://localhost:8000/health | python -m json.tool 2>/dev/null || echo "Backend not responding"'
