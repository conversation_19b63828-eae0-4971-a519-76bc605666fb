"use client"

import { useRef, useEffect, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Center } from "@react-three/drei"
import { useFrame } from "@react-three/fiber"
import { Lock, Server, CheckCircle, ArrowRight } from "lucide-react"
import type * as THREE from "three"
import AnimatedGradientText from "@/components/ui/animated-gradient-text"

function EncryptionAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.rotation.y = time * 0.5
      groupRef.current.position.y = Math.sin(time * 2) * 0.1
    }
  })

  return (
    <group ref={groupRef}>
      <mesh>
        <boxGeometry args={[1, 1.2, 0.2]} />
        <meshStandardMaterial color="#00FFD1" transparent opacity={0.7} />
      </mesh>
      <mesh position={[0, 0, 0.2]}>
        <torusGeometry args={[0.3, 0.1, 8, 16]} />
        <meshStandardMaterial color="#A3A3FF" emissive="#A3A3FF" emissiveIntensity={0.3} />
      </mesh>
      {/* Floating particles */}
      {Array.from({ length: 8 }).map((_, i) => (
        <mesh key={i} position={[(Math.random() - 0.5) * 3, (Math.random() - 0.5) * 3, (Math.random() - 0.5) * 2]}>
          <sphereGeometry args={[0.02]} />
          <meshStandardMaterial color="#00FFD1" emissive="#00FFD1" emissiveIntensity={0.5} />
        </mesh>
      ))}
    </group>
  )
}

function ServerAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.children.forEach((child, i) => {
        if (child.type === "Mesh") {
          child.position.y = Math.sin(time + i) * 0.1
        }
      })
    }
  })

  return (
    <group ref={groupRef}>
      {Array.from({ length: 3 }).map((_, i) => (
        <mesh key={i} position={[0, i * 0.4 - 0.4, 0]}>
          <boxGeometry args={[1.5, 0.3, 0.8]} />
          <meshStandardMaterial color="#00FFD1" transparent opacity={0.6} emissive="#00FFD1" emissiveIntensity={0.1} />
        </mesh>
      ))}
      {/* Glowing nodes */}
      {Array.from({ length: 6 }).map((_, i) => (
        <mesh key={i} position={[((i % 3) - 1) * 0.4, Math.floor(i / 3) * 0.4 - 0.2, 0.5]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial color="#A3A3FF" emissive="#A3A3FF" emissiveIntensity={0.8} />
        </mesh>
      ))}
      {/* Data flow lines */}
      {Array.from({ length: 4 }).map((_, i) => (
        <mesh key={`line-${i}`} position={[0, 0, 0.8]} rotation={[0, 0, (i * Math.PI) / 2]}>
          <cylinderGeometry args={[0.01, 0.01, 2, 8]} />
          <meshStandardMaterial color="#A3A3FF" transparent opacity={0.4} />
        </mesh>
      ))}
    </group>
  )
}

function ProofAnimation({ isVisible }: { isVisible: boolean }) {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current && isVisible) {
      const time = state.clock.getElapsedTime()
      groupRef.current.rotation.z = Math.sin(time) * 0.2
      groupRef.current.rotation.x = Math.cos(time * 0.5) * 0.1
    }
  })

  return (
    <group ref={groupRef}>
      <mesh>
        <octahedronGeometry args={[0.8]} />
        <meshStandardMaterial color="#00FFD1" wireframe transparent opacity={0.8} />
      </mesh>
      <mesh>
        <sphereGeometry args={[0.3]} />
        <meshStandardMaterial color="#A3A3FF" emissive="#A3A3FF" emissiveIntensity={0.5} transparent opacity={0.8} />
      </mesh>
      {/* Orbiting elements */}
      {Array.from({ length: 3 }).map((_, i) => (
        <mesh key={i} position={[Math.cos((i * Math.PI * 2) / 3) * 1.2, Math.sin((i * Math.PI * 2) / 3) * 1.2, 0]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial color="#00FFD1" emissive="#00FFD1" emissiveIntensity={0.6} />
        </mesh>
      ))}
    </group>
  )
}

function FeatureCard({
  title,
  description,
  icon: Icon,
  animation: Animation,
  isVisible,
  index,
}: {
  title: string
  description: string
  icon: any
  animation: any
  isVisible: boolean
  index: number
}) {
  return (
    <div
      className={`group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-3xl p-8 border border-cyan-400/20 transition-all duration-1000 hover:border-cyan-400/40 hover:shadow-2xl hover:shadow-cyan-400/10 ${
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-20"
      }`}
      style={{ transitionDelay: `${index * 200}ms` }}
    >
      {/* Background gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-blue-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-br from-cyan-400/20 to-blue-500/20 rounded-2xl group-hover:from-cyan-400/30 group-hover:to-blue-500/30 transition-all duration-300">
              <Icon className="w-8 h-8 text-cyan-400" />
            </div>
            <h3 className="text-2xl font-bold font-orbitron text-white group-hover:text-cyan-100 transition-colors">
              {title}
            </h3>
          </div>
          <ArrowRight className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 group-hover:translate-x-1 transition-all duration-300" />
        </div>

        {/* 3D Animation */}
        <div className="h-64 mb-6 rounded-2xl overflow-hidden bg-gradient-to-br from-indigo-950/50 to-slate-900/50">
          <Canvas camera={{ position: [0, 0, 3] }}>
            <Environment preset="night" />
            <ambientLight intensity={0.4} />
            <pointLight position={[5, 5, 5]} intensity={1} color="#00FFD1" />
            <pointLight position={[-5, -5, -5]} intensity={0.5} color="#A3A3FF" />
            <Center>
              <Animation isVisible={isVisible} />
            </Center>
          </Canvas>
        </div>

        {/* Description */}
        <p className="text-gray-300 leading-relaxed mb-6 group-hover:text-gray-200 transition-colors">{description}</p>

        {/* Learn More Button */}
        <button className="inline-flex items-center space-x-2 text-cyan-400 hover:text-cyan-300 font-semibold transition-colors group/btn">
          <span>Learn More</span>
          <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
        </button>
      </div>
    </div>
  )
}

export default function EnhancedFeaturesSection() {
  const [visiblePanels, setVisiblePanels] = useState<boolean[]>([false, false, false])
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            setVisiblePanels((prev) => {
              const newState = [...prev]
              newState[index] = true
              return newState
            })
          }
        })
      },
      { threshold: 0.2 },
    )

    if (sectionRef.current) {
      const panels = sectionRef.current.querySelectorAll(".feature-panel")
      panels?.forEach((panel) => observer.observe(panel))
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  const features = [
    {
      title: "Seamless Encryption",
      description:
        "Your pitch decks are encrypted end-to-end before any analysis begins. Our advanced encryption protocols ensure your intellectual property remains completely secure throughout the evaluation process, using military-grade AES-256 encryption.",
      icon: Lock,
      animation: EncryptionAnimation,
    },
    {
      title: "AI Scoring in Secure Enclave",
      description:
        "Our AI models operate within secure enclaves, processing your data in isolated environments. This ensures that even our systems cannot access your raw pitch content while providing accurate scoring and detailed feedback on your presentation.",
      icon: Server,
      animation: ServerAnimation,
    },
    {
      title: "Zero-Knowledge Proof",
      description:
        "Receive comprehensive evaluation results without exposing your pitch content. Our zero-knowledge proof system validates the analysis while maintaining complete privacy of your business information and competitive advantages.",
      icon: CheckCircle,
      animation: ProofAnimation,
    },
  ]

  return (
    <section
      id="features"
      ref={sectionRef}
      className="py-32 px-6 bg-gradient-to-b from-indigo-950 via-slate-900 to-indigo-950 relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-cyan-400/5 via-transparent to-transparent"></div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="mb-6">
            <AnimatedGradientText>
              <span className="text-white/90">Revolutionary Technology</span>
            </AnimatedGradientText>
          </div>

          <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold font-orbitron text-white mb-8 leading-tight">
            <span className="bg-gradient-to-r from-white via-cyan-400 to-white bg-clip-text text-transparent">
              Privacy-First
            </span>
            <br />
            <span className="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">Features</span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Experience the future of pitch evaluation with our cutting-edge privacy-preserving technology
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="feature-panel">
              <FeatureCard
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                animation={feature.animation}
                isVisible={visiblePanels[index]}
                index={index}
              />
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <button className="group bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-bold py-4 px-8 rounded-2xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-400/25 flex items-center space-x-2 mx-auto">
            <span>Explore All Features</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </button>
        </div>
      </div>
    </section>
  )
}
