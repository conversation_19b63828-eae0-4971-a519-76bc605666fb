FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Install snarkjs globally
RUN npm install -g snarkjs

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.enclave.txt .
RUN pip install --no-cache-dir -r requirements.enclave.txt

# Copy TEE private key
COPY keys/tee_private_key.pem ./tee_private_key.pem

# Copy model files (placeholder for now)
COPY enclave/model/ ./model/

# Copy ZK circuit files
COPY enclave/zk-circuit/ ./zk-circuit/

# Copy enclave application
COPY enclave/enclave.py ./enclave.py

# Create data directory
RUN mkdir -p /data

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Run the enclave service
CMD ["python", "enclave.py"]
