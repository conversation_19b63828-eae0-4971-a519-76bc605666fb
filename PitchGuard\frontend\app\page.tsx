"use client"

import { useState, useEffect } from "react"
import EnhancedHeroSection from "@/components/enhanced-hero-section"
import EnhancedFeaturesSection from "@/components/enhanced-features-section"
import TimelineSection from "@/components/timeline-section"
import TeamSection from "@/components/team-section"
import Footer from "@/components/footer"
import EnhancedNavigation from "@/components/enhanced-navigation"

export default function Home() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 via-slate-900 to-indigo-950 text-white overflow-x-hidden flex items-center justify-center">
        <div className="text-center">
          <div className="w-32 h-32 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mb-4 mx-auto"></div>
          <div className="text-white text-lg font-orbitron">Loading PitchGuard</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 via-slate-900 to-indigo-950 text-white overflow-x-hidden">
      <EnhancedNavigation />

      <main>
        <EnhancedHeroSection />
        <EnhancedFeaturesSection />
        <TimelineSection />
        <TeamSection />
      </main>

      <Footer />
    </div>
  )
}
