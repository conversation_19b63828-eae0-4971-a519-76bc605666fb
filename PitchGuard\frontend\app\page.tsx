'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import BlurText from '../components/BlurText';
import Dock from '../components/Dock';
import Aurora from '../components/Aurora';
import ScrollFloat from '../components/ScrollFloat';

// Icons for the dock
const HomeIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
  </svg>
);

const UploadIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
  </svg>
);

const ScoreIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
  </svg>
);

const VerifyIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
  </svg>
);

const InfoIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
  </svg>
);

export default function Home() {
  const [currentSection, setCurrentSection] = useState('home');
  const [currentPitchId, setCurrentPitchId] = useState<string | null>(null);
  const [scores, setScores] = useState<any>(null);
  const [zkProof, setZkProof] = useState<any>(null);

  const dockItems = [
    {
      icon: <HomeIcon />,
      label: "Home",
      onClick: () => setCurrentSection('home'),
    },
    {
      icon: <UploadIcon />,
      label: "Upload Pitch",
      onClick: () => setCurrentSection('upload'),
    },
    {
      icon: <ScoreIcon />,
      label: "View Scores",
      onClick: () => setCurrentSection('scores'),
    },
    {
      icon: <VerifyIcon />,
      label: "Verify Proof",
      onClick: () => setCurrentSection('verify'),
    },
    {
      icon: <InfoIcon />,
      label: "About",
      onClick: () => setCurrentSection('about'),
    },
  ];

  const renderSection = () => {
    switch (currentSection) {
      case 'home':
        return (
          <div className="relative min-h-screen flex flex-col items-center justify-center text-center">
            <Aurora
              colorStops={["#3b82f6", "#8b5cf6", "#06b6d4"]}
              amplitude={1.2}
              blend={0.6}
              speed={0.8}
            />
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="relative z-10 max-w-4xl mx-auto px-4"
            >
              <BlurText
                text="PitchGuard"
                className="text-6xl md:text-8xl font-bold text-white mb-6"
                animateBy="letters"
                delay={100}
              />
              <BlurText
                text="Secure Pitch Evaluation with TEE and Zero-Knowledge Proofs"
                className="text-xl md:text-2xl text-gray-200 mb-8"
                animateBy="words"
                delay={50}
              />
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                className="space-y-4"
              >
                <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                  Protect your pitch confidentiality while ensuring transparent and verifiable evaluation
                  using cutting-edge cryptographic techniques.
                </p>
                <div className="flex flex-wrap justify-center gap-4 mt-8">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                    <div className="text-sm font-medium text-white">🔐 Client-Side Encryption</div>
                    <div className="text-xs text-gray-300">AES-GCM + RSA</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                    <div className="text-sm font-medium text-white">🛡️ TEE Processing</div>
                    <div className="text-xs text-gray-300">Secure Enclave</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                    <div className="text-sm font-medium text-white">🔍 ZK Proofs</div>
                    <div className="text-xs text-gray-300">Verifiable Scoring</div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        );

      case 'upload':
        return (
          <div className="min-h-screen py-16">
            <div className="max-w-4xl mx-auto px-4">
              <ScrollFloat containerClassName="text-4xl font-bold text-center mb-12">
                Submit Your Pitch
              </ScrollFloat>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="card">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Upload Document
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Upload your pitch document for secure, encrypted evaluation. Your pitch will be
                    encrypted client-side and processed in a trusted execution environment.
                  </p>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <UploadIcon />
                    <p className="mt-2 text-sm text-gray-600">
                      Drag and drop your pitch document here, or click to browse
                    </p>
                    <button className="btn-primary mt-4">
                      Choose File
                    </button>
                  </div>
                </div>

                <div className="card">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">
                    How It Works
                  </h3>
                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        1
                      </div>
                      <div>
                        <strong>Client-side Encryption:</strong> Your pitch is encrypted in your browser
                        using AES-GCM with a random key.
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        2
                      </div>
                      <div>
                        <strong>TEE Processing:</strong> The encrypted pitch is processed in a trusted
                        execution environment using a fine-tuned RoBERTa model.
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        3
                      </div>
                      <div>
                        <strong>ZK Proof Generation:</strong> A zero-knowledge proof is generated to
                        verify the scoring was done correctly without revealing the pitch content.
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        4
                      </div>
                      <div>
                        <strong>Encrypted Results:</strong> Scores are encrypted with your public key
                        and can only be decrypted by you.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'scores':
        return (
          <div className="min-h-screen py-16">
            <div className="max-w-4xl mx-auto px-4">
              <ScrollFloat containerClassName="text-4xl font-bold text-center mb-12">
                Evaluation Results
              </ScrollFloat>
              {currentPitchId ? (
                <div className="card">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">
                    Pitch Evaluation Scores
                  </h3>
                  {scores ? (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{scores.narrative_clarity}/5</div>
                        <div className="text-sm text-gray-600">Narrative Clarity</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{scores.originality}/5</div>
                        <div className="text-sm text-gray-600">Originality</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{scores.team_strength}/5</div>
                        <div className="text-sm text-gray-600">Team Strength</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{scores.market_fit}/5</div>
                        <div className="text-sm text-gray-600">Market Fit</div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                      <p className="mt-4 text-gray-600">Decrypting scores...</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-16">
                  <ScoreIcon />
                  <p className="mt-4 text-gray-600">No pitch submitted yet. Upload a pitch to see evaluation results.</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'verify':
        return (
          <div className="min-h-screen py-16">
            <div className="max-w-4xl mx-auto px-4">
              <ScrollFloat containerClassName="text-4xl font-bold text-center mb-12">
                Zero-Knowledge Proof Verification
              </ScrollFloat>
              <div className="card">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Verify Scoring Integrity
                </h3>
                <p className="text-gray-600 mb-6">
                  Verify that the scores were computed correctly without revealing the pitch content
                  using zero-knowledge proofs.
                </p>
                {zkProof ? (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <VerifyIcon />
                        <span className="ml-2 text-green-800 font-medium">Proof Available</span>
                      </div>
                      <p className="text-green-700 text-sm mt-2">
                        Zero-knowledge proof generated successfully. Click verify to check integrity.
                      </p>
                    </div>
                    <button className="btn-primary">
                      Verify Proof
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <VerifyIcon />
                    <p className="mt-4">No proof available. Submit and decrypt a pitch first.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'about':
        return (
          <div className="min-h-screen py-16">
            <div className="max-w-4xl mx-auto px-4">
              <ScrollFloat containerClassName="text-4xl font-bold text-center mb-12">
                About PitchGuard
              </ScrollFloat>
              <div className="space-y-8">
                <div className="card">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Privacy Guarantees
                  </h3>
                  <div className="space-y-2 text-sm text-gray-700">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Your pitch content is never stored in plaintext
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Scoring happens in a trusted execution environment
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Zero-knowledge proofs verify correctness without revealing data
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Only you can decrypt your evaluation results
                    </div>
                  </div>
                </div>

                <div className="card">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Technical Architecture
                  </h3>
                  <p className="text-gray-600 mb-4">
                    PitchGuard uses a combination of modern cryptographic techniques to ensure
                    both privacy and verifiability in the pitch evaluation process.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-lg font-semibold text-blue-900">Frontend</div>
                      <div className="text-sm text-blue-700">Next.js + Web Crypto API</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-lg font-semibold text-green-900">Backend</div>
                      <div className="text-sm text-green-700">FastAPI + Docker</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-lg font-semibold text-purple-900">TEE Enclave</div>
                      <div className="text-sm text-purple-700">RoBERTa + ZK Circuits</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {renderSection()}
      <Dock items={dockItems} />
    </div>
  );
}
