#!/usr/bin/env python3
"""
PitchGuard FastAPI Backend
Handles pitch submissions, score retrieval, and proof verification
"""

import os
import json
import uuid
import subprocess
import tempfile
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>, HTTPException, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
import httpx
import uvicorn

app = FastAPI(title="PitchGuard Backend", version="1.0.0")

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
ENCLAVE_URL = os.getenv("ENCLAVE_URL", "http://localhost:5001")
DATA_DIR = "/data"
SUBMISSIONS_FILE = os.path.join(DATA_DIR, "submissions.json")
TEE_PUBLIC_KEY_PATH = "/app/keys/tee_public_key.pem"
ZK_VERIFICATION_KEY_PATH = "/app/zk-circuit/verification_key.json"

# Request/Response models
class PitchSubmissionRequest(BaseModel):
    encrypted_aes_key: str
    iv: str
    tag: str
    encrypted_pitch: str
    founder_public_key: str

class PitchSubmissionResponse(BaseModel):
    status: str
    pitch_id: str
    message: str

class ProofVerificationRequest(BaseModel):
    proof: Dict[str, Any]
    public_signals: list

class ProofVerificationResponse(BaseModel):
    valid: bool
    message: str

# Storage utilities
def load_submissions() -> Dict[str, Any]:
    """Load submissions from JSON file"""
    try:
        if os.path.exists(SUBMISSIONS_FILE):
            with open(SUBMISSIONS_FILE, 'r') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Error loading submissions: {e}")
        return {}

def save_submissions(submissions: Dict[str, Any]):
    """Save submissions to JSON file"""
    try:
        os.makedirs(DATA_DIR, exist_ok=True)
        with open(SUBMISSIONS_FILE, 'w') as f:
            json.dump(submissions, f, indent=2)
    except Exception as e:
        print(f"Error saving submissions: {e}")

def save_encrypted_result(pitch_id: str, encrypted_result: str):
    """Save encrypted result to file"""
    try:
        result_file = os.path.join(DATA_DIR, f"{pitch_id}.bin")
        with open(result_file, 'w') as f:
            f.write(encrypted_result)
    except Exception as e:
        print(f"Error saving encrypted result: {e}")

def load_encrypted_result(pitch_id: str) -> Optional[str]:
    """Load encrypted result from file"""
    try:
        result_file = os.path.join(DATA_DIR, f"{pitch_id}.bin")
        if os.path.exists(result_file):
            with open(result_file, 'r') as f:
                return f.read()
        return None
    except Exception as e:
        print(f"Error loading encrypted result: {e}")
        return None

# API endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "PitchGuard Backend API",
        "version": "1.0.0",
        "endpoints": {
            "submit_pitch": "POST /submit-pitch",
            "retrieve_score": "GET /retrieve-score/{pitch_id}",
            "verify_proof": "POST /verify-proof",
            "tee_pubkey": "GET /tee-pubkey"
        }
    }

@app.get("/tee-pubkey")
async def get_tee_public_key():
    """Get TEE public key for encryption"""
    try:
        if os.path.exists(TEE_PUBLIC_KEY_PATH):
            return FileResponse(
                TEE_PUBLIC_KEY_PATH,
                media_type="application/x-pem-file",
                filename="tee_public_key.pem"
            )
        else:
            raise HTTPException(status_code=404, detail="TEE public key not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving TEE public key: {e}")

@app.post("/submit-pitch", response_model=PitchSubmissionResponse)
async def submit_pitch(request: PitchSubmissionRequest):
    """Submit encrypted pitch for scoring"""
    try:
        # Generate unique pitch ID
        pitch_id = str(uuid.uuid4())
        
        # Forward request to enclave
        async with httpx.AsyncClient() as client:
            enclave_response = await client.post(
                f"{ENCLAVE_URL}/process-pitch",
                json=request.dict(),
                timeout=60.0
            )
            
            if enclave_response.status_code != 200:
                raise HTTPException(
                    status_code=500,
                    detail=f"Enclave processing failed: {enclave_response.text}"
                )
            
            enclave_data = enclave_response.json()
            encrypted_result = enclave_data["encrypted_result"]
        
        # Save encrypted result
        save_encrypted_result(pitch_id, encrypted_result)
        
        # Update submissions tracking
        submissions = load_submissions()
        submissions[pitch_id] = {
            "status": "complete",
            "timestamp": datetime.now().isoformat(),
            "result_file": f"{pitch_id}.bin"
        }
        save_submissions(submissions)
        
        return PitchSubmissionResponse(
            status="complete",
            pitch_id=pitch_id,
            message="Pitch processed successfully"
        )
        
    except httpx.RequestError as e:
        raise HTTPException(status_code=503, detail=f"Enclave service unavailable: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing pitch: {e}")

@app.get("/retrieve-score/{pitch_id}")
async def retrieve_score(pitch_id: str):
    """Retrieve encrypted scores for a pitch"""
    try:
        # Check if pitch exists
        submissions = load_submissions()
        if pitch_id not in submissions:
            raise HTTPException(status_code=404, detail="Pitch not found")
        
        submission = submissions[pitch_id]
        if submission["status"] != "complete":
            return {"status": "pending", "message": "Scores not ready yet"}
        
        # Load encrypted result
        encrypted_result = load_encrypted_result(pitch_id)
        if encrypted_result is None:
            raise HTTPException(status_code=404, detail="Encrypted result not found")
        
        return {
            "status": "complete",
            "encrypted_result": encrypted_result,
            "timestamp": submission["timestamp"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving score: {e}")

@app.post("/verify-proof", response_model=ProofVerificationResponse)
async def verify_proof(request: ProofVerificationRequest):
    """Verify ZK proof"""
    try:
        # Check if proof is mock (for MVP)
        if request.proof.get("mock"):
            return ProofVerificationResponse(
                valid=True,
                message="Mock proof verified (MVP mode)"
            )
        
        # Verify real proof using snarkjs
        with tempfile.TemporaryDirectory() as temp_dir:
            proof_file = os.path.join(temp_dir, "proof.json")
            public_file = os.path.join(temp_dir, "public.json")
            
            # Write proof and public signals
            with open(proof_file, 'w') as f:
                json.dump(request.proof, f)
            
            with open(public_file, 'w') as f:
                json.dump(request.public_signals, f)
            
            # Run snarkjs verify
            verify_cmd = [
                "snarkjs", "groth16", "verify",
                ZK_VERIFICATION_KEY_PATH,
                public_file,
                proof_file
            ]
            
            result = subprocess.run(
                verify_cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                return ProofVerificationResponse(
                    valid=True,
                    message="Proof verified successfully"
                )
            else:
                return ProofVerificationResponse(
                    valid=False,
                    message=f"Proof verification failed: {result.stderr}"
                )
                
    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Proof verification timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error verifying proof: {e}")

@app.get("/submissions")
async def list_submissions():
    """List all submissions (for demo/debugging)"""
    try:
        submissions = load_submissions()
        return {
            "submissions": submissions,
            "count": len(submissions)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing submissions: {e}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check enclave connectivity
        async with httpx.AsyncClient() as client:
            enclave_response = await client.get(f"{ENCLAVE_URL}/health", timeout=5.0)
            enclave_healthy = enclave_response.status_code == 200
    except:
        enclave_healthy = False
    
    return {
        "status": "healthy",
        "service": "PitchGuard Backend",
        "enclave_connected": enclave_healthy,
        "data_dir": DATA_DIR
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
