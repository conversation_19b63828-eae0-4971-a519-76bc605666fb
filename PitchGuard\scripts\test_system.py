#!/usr/bin/env python3
"""
PitchGuard System Test Script
Tests the complete flow from pitch submission to proof verification
"""

import os
import sys
import json
import time
import base64
import hashlib
import requests
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from scripts.generate_keys import generate_rsa_keypair, save_private_key, save_public_key
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

class PitchGuardTester:
    def __init__(self, backend_url="http://localhost:8000"):
        self.backend_url = backend_url
        self.session = requests.Session()
        
    def test_health_checks(self):
        """Test that all services are healthy"""
        print("Testing service health...")
        
        # Test backend
        try:
            response = self.session.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                print("✓ Backend service healthy")
                health_data = response.json()
                if health_data.get("enclave_connected"):
                    print("✓ Enclave service connected")
                else:
                    print("⚠ Enclave service not connected")
            else:
                print(f"✗ Backend service unhealthy: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Backend service unreachable: {e}")
            return False
        
        return True
    
    def get_tee_public_key(self):
        """Get TEE public key from backend"""
        try:
            response = self.session.get(f"{self.backend_url}/tee-pubkey")
            if response.status_code == 200:
                return response.text
            else:
                print(f"Error getting TEE public key: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error getting TEE public key: {e}")
            return None
    
    def generate_test_keys(self):
        """Generate test founder keys"""
        print("Generating test founder keys...")
        private_key, public_key = generate_rsa_keypair()
        
        # Export keys
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ).decode('utf-8')
        
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        ).decode('utf-8')
        
        return private_key, public_key, private_pem, public_pem
    
    def encrypt_pitch(self, pitch_text, tee_public_key_pem):
        """Encrypt pitch using the same method as frontend"""
        # Generate AES key
        aes_key = os.urandom(32)  # 256-bit key
        
        # Encrypt pitch with AES-GCM
        iv = os.urandom(12)  # 96-bit IV for GCM
        cipher = Cipher(algorithms.AES(aes_key), modes.GCM(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        pitch_bytes = pitch_text.encode('utf-8')
        ciphertext = encryptor.update(pitch_bytes) + encryptor.finalize()
        tag = encryptor.tag
        
        # Encrypt AES key with TEE public key
        tee_public_key = serialization.load_pem_public_key(
            tee_public_key_pem.encode(),
            backend=default_backend()
        )
        
        encrypted_aes_key = tee_public_key.encrypt(
            aes_key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return {
            "encrypted_aes_key": base64.b64encode(encrypted_aes_key).decode('utf-8'),
            "iv": base64.b64encode(iv).decode('utf-8'),
            "tag": base64.b64encode(tag).decode('utf-8'),
            "encrypted_pitch": base64.b64encode(ciphertext).decode('utf-8')
        }
    
    def submit_pitch(self, encrypted_data, founder_public_key_pem):
        """Submit encrypted pitch to backend"""
        payload = {
            **encrypted_data,
            "founder_public_key": founder_public_key_pem
        }
        
        try:
            response = self.session.post(
                f"{self.backend_url}/submit-pitch",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error submitting pitch: {response.status_code}")
                print(response.text)
                return None
        except Exception as e:
            print(f"Error submitting pitch: {e}")
            return None
    
    def retrieve_scores(self, pitch_id):
        """Retrieve encrypted scores"""
        try:
            response = self.session.get(f"{self.backend_url}/retrieve-score/{pitch_id}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error retrieving scores: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error retrieving scores: {e}")
            return None
    
    def decrypt_scores(self, encrypted_result, founder_private_key):
        """Decrypt scores using founder's private key"""
        try:
            encrypted_data = base64.b64decode(encrypted_result)
            
            decrypted_data = founder_private_key.decrypt(
                encrypted_data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return json.loads(decrypted_data.decode('utf-8'))
        except Exception as e:
            print(f"Error decrypting scores: {e}")
            return None
    
    def verify_proof(self, proof_data):
        """Verify ZK proof"""
        try:
            payload = {
                "proof": proof_data["zk_proof"],
                "public_signals": proof_data["public_signals"]
            }
            
            response = self.session.post(
                f"{self.backend_url}/verify-proof",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error verifying proof: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error verifying proof: {e}")
            return None
    
    def run_full_test(self):
        """Run complete system test"""
        print("PitchGuard System Test")
        print("=" * 50)
        
        # Test health
        if not self.test_health_checks():
            print("❌ Health checks failed")
            return False
        
        # Get TEE public key
        print("\nGetting TEE public key...")
        tee_public_key = self.get_tee_public_key()
        if not tee_public_key:
            print("❌ Failed to get TEE public key")
            return False
        print("✓ TEE public key retrieved")
        
        # Generate founder keys
        founder_private_key, founder_public_key, founder_private_pem, founder_public_pem = self.generate_test_keys()
        print("✓ Founder keys generated")
        
        # Load sample pitch
        sample_pitch_file = project_root / "data" / "sample_pitch.txt"
        if sample_pitch_file.exists():
            with open(sample_pitch_file, 'r') as f:
                pitch_text = f.read()
        else:
            pitch_text = "This is a test pitch for PitchGuard system verification."
        
        print(f"✓ Using pitch: {pitch_text[:50]}...")
        
        # Encrypt pitch
        print("\nEncrypting pitch...")
        encrypted_data = self.encrypt_pitch(pitch_text, tee_public_key)
        print("✓ Pitch encrypted")
        
        # Submit pitch
        print("\nSubmitting pitch...")
        submission_result = self.submit_pitch(encrypted_data, founder_public_pem)
        if not submission_result:
            print("❌ Failed to submit pitch")
            return False
        
        pitch_id = submission_result["pitch_id"]
        print(f"✓ Pitch submitted with ID: {pitch_id}")
        
        # Wait for processing
        print("\nWaiting for processing...")
        max_attempts = 12  # 60 seconds
        for attempt in range(max_attempts):
            time.sleep(5)
            scores_result = self.retrieve_scores(pitch_id)
            
            if scores_result and scores_result.get("status") == "complete":
                print("✓ Processing complete")
                break
            elif attempt == max_attempts - 1:
                print("❌ Processing timeout")
                return False
            else:
                print(f"⏳ Still processing... (attempt {attempt + 1}/{max_attempts})")
        
        # Decrypt scores
        print("\nDecrypting scores...")
        encrypted_result = scores_result["encrypted_result"]
        decrypted_scores = self.decrypt_scores(encrypted_result, founder_private_key)
        
        if not decrypted_scores:
            print("❌ Failed to decrypt scores")
            return False
        
        print("✓ Scores decrypted successfully")
        print(f"Scores: {decrypted_scores['scores']}")
        
        # Verify proof
        print("\nVerifying ZK proof...")
        proof_result = self.verify_proof(decrypted_scores)
        
        if not proof_result:
            print("❌ Failed to verify proof")
            return False
        
        if proof_result.get("valid"):
            print("✓ ZK proof verified successfully")
        else:
            print("❌ ZK proof verification failed")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! PitchGuard is working correctly.")
        return True

def main():
    """Main test function"""
    tester = PitchGuardTester()
    success = tester.run_full_test()
    
    if success:
        print("\n✅ System test completed successfully")
        sys.exit(0)
    else:
        print("\n❌ System test failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
