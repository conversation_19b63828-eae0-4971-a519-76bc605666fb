"use client"

import type React from "react"

import { forwardRef, useRef } from "react"
import { cn } from "@/lib/utils"

export interface AnimatedBeamProps {
  className?: string
  containerRef: React.RefObject<HTMLElement>
  fromRef: React.RefObject<HTMLElement>
  toRef: React.RefObject<HTMLElement>
  curvature?: number
  reverse?: boolean
  duration?: number
  delay?: number
  pathColor?: string
  pathWidth?: number
  pathOpacity?: number
  gradientStartColor?: string
  gradientStopColor?: string
  startXOffset?: number
  startYOffset?: number
  endXOffset?: number
  endYOffset?: number
}

export const AnimatedBeam = forwardRef<SVGSVGElement, AnimatedBeamProps>(
  (
    {
      className,
      containerRef,
      fromRef,
      toRef,
      curvature = 0,
      reverse = false,
      duration = Math.random() * 3 + 4,
      delay = 0,
      pathColor = "gray",
      pathWidth = 2,
      pathOpacity = 0.2,
      gradientStartColor = "#00FFD1",
      gradientStopColor = "#A3A3FF",
      startXOffset = 0,
      startYOffset = 0,
      endXOffset = 0,
      endYOffset = 0,
    },
    ref,
  ) => {
    const id = useRef(`beam-${Math.random()}`)

    return (
      <svg
        ref={ref}
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        className={cn("pointer-events-none absolute inset-0 transform-gpu", className)}
        style={{
          overflow: "visible",
        }}
      >
        <defs>
          <linearGradient id={id.current} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={gradientStartColor} stopOpacity="0" />
            <stop offset="50%" stopColor={gradientStartColor} stopOpacity="1" />
            <stop offset="100%" stopColor={gradientStopColor} stopOpacity="0" />
          </linearGradient>
        </defs>
        <path
          d="M 10,50 Q 50,10 90,50"
          stroke={`url(#${id.current})`}
          strokeWidth={pathWidth}
          fill="none"
          strokeLinecap="round"
          opacity={pathOpacity}
        >
          <animate
            attributeName="stroke-dasharray"
            values="0,100;50,100;100,100"
            dur={`${duration}s`}
            begin={`${delay}s`}
            repeatCount="indefinite"
          />
        </path>
      </svg>
    )
  },
)

AnimatedBeam.displayName = "AnimatedBeam"
