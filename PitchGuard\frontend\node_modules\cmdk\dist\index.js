"use client";var Ne=Object.create;var F=Object.defineProperty;var He=Object.getOwnPropertyDescriptor;var Ge=Object.getOwnPropertyNames;var Ve=Object.getPrototypeOf,Ke=Object.prototype.hasOwnProperty;var $e=(e,n)=>{for(var r in n)F(e,r,{get:n[r],enumerable:!0})},me=(e,n,r,l)=>{if(n&&typeof n=="object"||typeof n=="function")for(let a of Ge(n))!Ke.call(e,a)&&a!==r&&F(e,a,{get:()=>n[a],enumerable:!(l=He(n,a))||l.enumerable});return e};var pe=(e,n,r)=>(r=e!=null?Ne(Ve(e)):{},me(n||!e||!e.__esModule?F(r,"default",{value:e,enumerable:!0}):r,e)),Fe=e=>me(F({},"__esModule",{value:!0}),e);var ot={};$e(ot,{Command:()=>Qe,CommandDialog:()=>ke,CommandEmpty:()=>we,CommandGroup:()=>Me,CommandInput:()=>Le,CommandItem:()=>Te,CommandList:()=>_e,CommandLoading:()=>xe,CommandRoot:()=>ae,CommandSeparator:()=>De,defaultFilter:()=>Ce,useCommandState:()=>L});module.exports=Fe(ot);var _=pe(require("@radix-ui/react-dialog")),o=pe(require("react"));var ve=1,Ue=.9,je=.8,Be=.17,Q=.1,Z=.999,qe=.9999;var We=.99,Xe=/[\\\/_+.#"@\[\(\{&]/,Je=/[\\\/_+.#"@\[\(\{&]/g,Ye=/[\s-]/,ge=/[\s-]/g;function ee(e,n,r,l,a,u,f){if(u===n.length)return a===e.length?ve:We;var p=`${a},${u}`;if(f[p]!==void 0)return f[p];for(var g=l.charAt(u),m=r.indexOf(g,a),d=0,v,C,b,P;m>=0;)v=ee(e,n,r,l,m+1,u+1,f),v>d&&(m===a?v*=ve:Xe.test(e.charAt(m-1))?(v*=je,b=e.slice(a,m-1).match(Je),b&&a>0&&(v*=Math.pow(Z,b.length))):Ye.test(e.charAt(m-1))?(v*=Ue,P=e.slice(a,m-1).match(ge),P&&a>0&&(v*=Math.pow(Z,P.length))):(v*=Be,a>0&&(v*=Math.pow(Z,m-a))),e.charAt(m)!==n.charAt(u)&&(v*=qe)),(v<Q&&r.charAt(m-1)===l.charAt(u+1)||l.charAt(u+1)===l.charAt(u)&&r.charAt(m-1)!==l.charAt(u))&&(C=ee(e,n,r,l,m+1,u+2,f),C*Q>v&&(v=C*Q)),v>d&&(d=v),m=r.indexOf(g,m+1);return f[p]=d,d}function Re(e){return e.toLowerCase().replace(ge," ")}function he(e,n,r){return e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,ee(e,n,Re(e),Re(n),0,0,{})}var T=require("@radix-ui/react-primitive"),w=require("@radix-ui/react-id"),Se=require("use-sync-external-store/shim/index.js"),G='[cmdk-group=""]',te='[cmdk-group-items=""]',ze='[cmdk-group-heading=""]',ne='[cmdk-item=""]',Ee=`${ne}:not([aria-disabled="true"])`,re="cmdk-item-select",k="data-value",Ce=(e,n,r)=>he(e,n,r),be=o.createContext(void 0),K=()=>o.useContext(be),ye=o.createContext(void 0),oe=()=>o.useContext(ye),Pe=o.createContext(void 0);var ae=o.forwardRef((e,n)=>{let r=A(()=>{var t,s;return{search:"",value:(s=(t=e.value)!=null?t:e.defaultValue)!=null?s:"",filtered:{count:0,items:new Map,groups:new Set}}}),l=A(()=>new Set),a=A(()=>new Map),u=A(()=>new Map),f=A(()=>new Set),p=Ae(e),{label:g,children:m,value:d,onValueChange:v,filter:C,shouldFilter:b,loop:P,disablePointerSelection:Oe=!1,vimBindings:j=!0,...I}=e,ie=(0,w.useId)(),B=(0,w.useId)(),$=(0,w.useId)(),M=o.useRef(null),E=tt();x(()=>{if(d!==void 0){let t=d.trim();r.current.value=t,S.emit()}},[d]),x(()=>{E(6,se)},[]);let S=o.useMemo(()=>({subscribe:t=>(f.current.add(t),()=>f.current.delete(t)),snapshot:()=>r.current,setState:(t,s,i)=>{var c,R,h;if(!Object.is(r.current[t],s)){if(r.current[t]=s,t==="search")J(),W(),E(1,X);else if(t==="value"&&(i||E(5,se),((c=p.current)==null?void 0:c.value)!==void 0)){let y=s!=null?s:"";(h=(R=p.current).onValueChange)==null||h.call(R,y);return}S.emit()}},emit:()=>{f.current.forEach(t=>t())}}),[]),q=o.useMemo(()=>({value:(t,s,i)=>{var c;s!==((c=u.current.get(t))==null?void 0:c.value)&&(u.current.set(t,{value:s,keywords:i}),r.current.filtered.items.set(t,ce(s,i)),E(2,()=>{W(),S.emit()}))},item:(t,s)=>(l.current.add(t),s&&(a.current.has(s)?a.current.get(s).add(t):a.current.set(s,new Set([t]))),E(3,()=>{J(),W(),r.current.value||X(),S.emit()}),()=>{u.current.delete(t),l.current.delete(t),r.current.filtered.items.delete(t);let i=O();E(4,()=>{J(),(i==null?void 0:i.getAttribute("id"))===t&&X(),S.emit()})}),group:t=>(a.current.has(t)||a.current.set(t,new Set),()=>{u.current.delete(t),a.current.delete(t)}),filter:()=>p.current.shouldFilter,label:g||e["aria-label"],getDisablePointerSelection:()=>p.current.disablePointerSelection,listId:ie,inputId:$,labelId:B,listInnerRef:M}),[]);function ce(t,s){var c,R;let i=(R=(c=p.current)==null?void 0:c.filter)!=null?R:Ce;return t?i(t,r.current.search,s):0}function W(){if(!r.current.search||p.current.shouldFilter===!1)return;let t=r.current.filtered.items,s=[];r.current.filtered.groups.forEach(c=>{let R=a.current.get(c),h=0;R.forEach(y=>{let D=t.get(y);h=Math.max(D,h)}),s.push([c,h])});let i=M.current;N().sort((c,R)=>{var D,H;let h=c.getAttribute("id"),y=R.getAttribute("id");return((D=t.get(y))!=null?D:0)-((H=t.get(h))!=null?H:0)}).forEach(c=>{let R=c.closest(te);R?R.appendChild(c.parentElement===R?c:c.closest(`${te} > *`)):i.appendChild(c.parentElement===i?c:c.closest(`${te} > *`))}),s.sort((c,R)=>R[1]-c[1]).forEach(c=>{var h;let R=(h=M.current)==null?void 0:h.querySelector(`${G}[${k}="${encodeURIComponent(c[0])}"]`);R==null||R.parentElement.appendChild(R)})}function X(){let t=N().find(i=>i.getAttribute("aria-disabled")!=="true"),s=t==null?void 0:t.getAttribute(k);S.setState("value",s||void 0)}function J(){var s,i,c,R;if(!r.current.search||p.current.shouldFilter===!1){r.current.filtered.count=l.current.size;return}r.current.filtered.groups=new Set;let t=0;for(let h of l.current){let y=(i=(s=u.current.get(h))==null?void 0:s.value)!=null?i:"",D=(R=(c=u.current.get(h))==null?void 0:c.keywords)!=null?R:[],H=ce(y,D);r.current.filtered.items.set(h,H),H>0&&t++}for(let[h,y]of a.current)for(let D of y)if(r.current.filtered.items.get(D)>0){r.current.filtered.groups.add(h);break}r.current.filtered.count=t}function se(){var s,i,c;let t=O();t&&(((s=t.parentElement)==null?void 0:s.firstChild)===t&&((c=(i=t.closest(G))==null?void 0:i.querySelector(ze))==null||c.scrollIntoView({block:"nearest"})),t.scrollIntoView({block:"nearest"}))}function O(){var t;return(t=M.current)==null?void 0:t.querySelector(`${ne}[aria-selected="true"]`)}function N(){var t;return Array.from(((t=M.current)==null?void 0:t.querySelectorAll(Ee))||[])}function Y(t){let i=N()[t];i&&S.setState("value",i.getAttribute(k))}function z(t){var h;let s=O(),i=N(),c=i.findIndex(y=>y===s),R=i[c+t];(h=p.current)!=null&&h.loop&&(R=c+t<0?i[i.length-1]:c+t===i.length?i[0]:i[c+t]),R&&S.setState("value",R.getAttribute(k))}function le(t){let s=O(),i=s==null?void 0:s.closest(G),c;for(;i&&!c;)i=t>0?Ze(i,G):et(i,G),c=i==null?void 0:i.querySelector(Ee);c?S.setState("value",c.getAttribute(k)):z(t)}let ue=()=>Y(N().length-1),de=t=>{t.preventDefault(),t.metaKey?ue():t.altKey?le(1):z(1)},fe=t=>{t.preventDefault(),t.metaKey?Y(0):t.altKey?le(-1):z(-1)};return o.createElement(T.Primitive.div,{ref:n,tabIndex:-1,...I,"cmdk-root":"",onKeyDown:t=>{var s;if((s=I.onKeyDown)==null||s.call(I,t),!t.defaultPrevented)switch(t.key){case"n":case"j":{j&&t.ctrlKey&&de(t);break}case"ArrowDown":{de(t);break}case"p":case"k":{j&&t.ctrlKey&&fe(t);break}case"ArrowUp":{fe(t);break}case"Home":{t.preventDefault(),Y(0);break}case"End":{t.preventDefault(),ue();break}case"Enter":if(!t.nativeEvent.isComposing&&t.keyCode!==229){t.preventDefault();let i=O();if(i){let c=new Event(re);i.dispatchEvent(c)}}}}},o.createElement("label",{"cmdk-label":"",htmlFor:q.inputId,id:q.labelId,style:nt},g),U(e,t=>o.createElement(ye.Provider,{value:S},o.createElement(be.Provider,{value:q},t))))}),Te=o.forwardRef((e,n)=>{var $,M;let r=(0,w.useId)(),l=o.useRef(null),a=o.useContext(Pe),u=K(),f=Ae(e),p=(M=($=f.current)==null?void 0:$.forceMount)!=null?M:a==null?void 0:a.forceMount;x(()=>{if(!p)return u.item(r,a==null?void 0:a.id)},[p]);let g=Ie(r,l,[e.value,e.children,l],e.keywords),m=oe(),d=L(E=>E.value&&E.value===g.current),v=L(E=>p||u.filter()===!1?!0:E.search?E.filtered.items.get(r)>0:!0);o.useEffect(()=>{let E=l.current;if(!(!E||e.disabled))return E.addEventListener(re,C),()=>E.removeEventListener(re,C)},[v,e.onSelect,e.disabled]);function C(){var E,S;b(),(S=(E=f.current).onSelect)==null||S.call(E,g.current)}function b(){m.setState("value",g.current,!0)}if(!v)return null;let{disabled:P,value:Oe,onSelect:j,forceMount:I,keywords:ie,...B}=e;return o.createElement(T.Primitive.div,{ref:V([l,n]),...B,id:r,"cmdk-item":"",role:"option","aria-disabled":!!P,"aria-selected":!!d,"data-disabled":!!P,"data-selected":!!d,onPointerMove:P||u.getDisablePointerSelection()?void 0:b,onClick:P?void 0:C},e.children)}),Me=o.forwardRef((e,n)=>{let{heading:r,children:l,forceMount:a,...u}=e,f=(0,w.useId)(),p=o.useRef(null),g=o.useRef(null),m=(0,w.useId)(),d=K(),v=L(b=>a||d.filter()===!1?!0:b.search?b.filtered.groups.has(f):!0);x(()=>d.group(f),[]),Ie(f,p,[e.value,e.heading,g]);let C=o.useMemo(()=>({id:f,forceMount:a}),[a]);return o.createElement(T.Primitive.div,{ref:V([p,n]),...u,"cmdk-group":"",role:"presentation",hidden:v?void 0:!0},r&&o.createElement("div",{ref:g,"cmdk-group-heading":"","aria-hidden":!0,id:m},r),U(e,b=>o.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?m:void 0},o.createElement(Pe.Provider,{value:C},b))))}),De=o.forwardRef((e,n)=>{let{alwaysRender:r,...l}=e,a=o.useRef(null),u=L(f=>!f.search);return!r&&!u?null:o.createElement(T.Primitive.div,{ref:V([a,n]),...l,"cmdk-separator":"",role:"separator"})}),Le=o.forwardRef((e,n)=>{let{onValueChange:r,...l}=e,a=e.value!=null,u=oe(),f=L(d=>d.search),p=L(d=>d.value),g=K(),m=o.useMemo(()=>{var v;let d=(v=g.listInnerRef.current)==null?void 0:v.querySelector(`${ne}[${k}="${encodeURIComponent(p)}"]`);return d==null?void 0:d.getAttribute("id")},[]);return o.useEffect(()=>{e.value!=null&&u.setState("search",e.value)},[e.value]),o.createElement(T.Primitive.input,{ref:n,...l,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":g.listId,"aria-labelledby":g.labelId,"aria-activedescendant":m,id:g.inputId,type:"text",value:a?e.value:f,onChange:d=>{a||u.setState("search",d.target.value),r==null||r(d.target.value)}})}),_e=o.forwardRef((e,n)=>{let{children:r,label:l="Suggestions",...a}=e,u=o.useRef(null),f=o.useRef(null),p=K();return o.useEffect(()=>{if(f.current&&u.current){let g=f.current,m=u.current,d,v=new ResizeObserver(()=>{d=requestAnimationFrame(()=>{let C=g.offsetHeight;m.style.setProperty("--cmdk-list-height",C.toFixed(1)+"px")})});return v.observe(g),()=>{cancelAnimationFrame(d),v.unobserve(g)}}},[]),o.createElement(T.Primitive.div,{ref:V([u,n]),...a,"cmdk-list":"",role:"listbox","aria-label":l,id:p.listId},U(e,g=>o.createElement("div",{ref:V([f,p.listInnerRef]),"cmdk-list-sizer":""},g)))}),ke=o.forwardRef((e,n)=>{let{open:r,onOpenChange:l,overlayClassName:a,contentClassName:u,container:f,...p}=e;return o.createElement(_.Root,{open:r,onOpenChange:l},o.createElement(_.Portal,{container:f},o.createElement(_.Overlay,{"cmdk-overlay":"",className:a}),o.createElement(_.Content,{"aria-label":e.label,"cmdk-dialog":"",className:u},o.createElement(ae,{ref:n,...p}))))}),we=o.forwardRef((e,n)=>L(l=>l.filtered.count===0)?o.createElement(T.Primitive.div,{ref:n,...e,"cmdk-empty":"",role:"presentation"}):null),xe=o.forwardRef((e,n)=>{let{progress:r,children:l,label:a="Loading...",...u}=e;return o.createElement(T.Primitive.div,{ref:n,...u,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},U(e,f=>o.createElement("div",{"aria-hidden":!0},f)))}),Qe=Object.assign(ae,{List:_e,Item:Te,Input:Le,Group:Me,Separator:De,Dialog:ke,Empty:we,Loading:xe});function Ze(e,n){let r=e.nextElementSibling;for(;r;){if(r.matches(n))return r;r=r.nextElementSibling}}function et(e,n){let r=e.previousElementSibling;for(;r;){if(r.matches(n))return r;r=r.previousElementSibling}}function Ae(e){let n=o.useRef(e);return x(()=>{n.current=e}),n}var x=typeof window=="undefined"?o.useEffect:o.useLayoutEffect;function A(e){let n=o.useRef();return n.current===void 0&&(n.current=e()),n}function V(e){return n=>{e.forEach(r=>{typeof r=="function"?r(n):r!=null&&(r.current=n)})}}function L(e){let n=oe(),r=()=>e(n.snapshot());return(0,Se.useSyncExternalStore)(n.subscribe,r,r)}function Ie(e,n,r,l=[]){let a=o.useRef(),u=K();return x(()=>{var g;let f=(()=>{var m;for(let d of r){if(typeof d=="string")return d.trim();if(typeof d=="object"&&"current"in d)return d.current?(m=d.current.textContent)==null?void 0:m.trim():a.current}})(),p=l.map(m=>m.trim());u.value(e,f,p),(g=n.current)==null||g.setAttribute(k,f),a.current=f}),a}var tt=()=>{let[e,n]=o.useState(),r=A(()=>new Map);return x(()=>{r.current.forEach(l=>l()),r.current=new Map},[e]),(l,a)=>{r.current.set(l,a),n({})}};function rt(e){let n=e.type;return typeof n=="function"?n(e.props):"render"in n?n.render(e.props):e}function U({asChild:e,children:n},r){return e&&o.isValidElement(n)?o.cloneElement(rt(n),{ref:n.ref},r(n.props.children)):r(n)}var nt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};0&&(module.exports={Command,CommandDialog,CommandEmpty,CommandGroup,CommandInput,CommandItem,CommandList,CommandLoading,CommandRoot,CommandSeparator,defaultFilter,useCommandState});
