import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface MorphingTextProps {
  texts: string[];
  interval?: number;
  className?: string;
  morphDuration?: number;
}

const MorphingText: React.FC<MorphingTextProps> = ({
  texts,
  interval = 3000,
  className = '',
  morphDuration = 0.8,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (texts.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % texts.length);
    }, interval);

    return () => clearInterval(timer);
  }, [texts.length, interval]);

  const currentText = texts[currentIndex] || '';

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ 
            opacity: 0,
            y: 20,
            filter: 'blur(10px)',
            scale: 0.9
          }}
          animate={{ 
            opacity: 1,
            y: 0,
            filter: 'blur(0px)',
            scale: 1
          }}
          exit={{ 
            opacity: 0,
            y: -20,
            filter: 'blur(10px)',
            scale: 1.1
          }}
          transition={{
            duration: morphDuration,
            ease: [0.4, 0, 0.2, 1],
          }}
          className="absolute inset-0 flex items-center justify-center"
        >
          {currentText.split('').map((char, index) => (
            <motion.span
              key={`${currentIndex}-${index}`}
              initial={{ 
                opacity: 0,
                y: 20,
                rotateX: -90,
              }}
              animate={{ 
                opacity: 1,
                y: 0,
                rotateX: 0,
              }}
              transition={{
                duration: morphDuration * 0.8,
                delay: index * 0.02,
                ease: [0.4, 0, 0.2, 1],
              }}
              style={{
                display: 'inline-block',
                transformOrigin: 'center bottom',
              }}
            >
              {char === ' ' ? '\u00A0' : char}
            </motion.span>
          ))}
        </motion.div>
      </AnimatePresence>
      
      {/* Invisible text to maintain container size */}
      <div className="invisible" aria-hidden="true">
        {texts.reduce((longest, text) => 
          text.length > longest.length ? text : longest, ''
        )}
      </div>
    </div>
  );
};

export default MorphingText;
