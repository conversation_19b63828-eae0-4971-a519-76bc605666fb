{"name": "glsl-noise", "version": "0.0.0", "description": "webgl-noise shaders ported to work with glslify", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/hughsk/glsl-noise.git"}, "keywords": ["glsl", "noise", "shader", "perlin", "simplex", "webgl", "glslify"], "author": "<PERSON>, <PERSON><PERSON>, <PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/hughsk/glsl-noise/issues"}}