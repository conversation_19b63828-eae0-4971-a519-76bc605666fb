# PitchGuard Environment Configuration
# Copy this file to .env and customize as needed

# Backend Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
ENCLAVE_URL=http://enclave:5001

# Enclave Configuration  
ENCLAVE_HOST=0.0.0.0
ENCLAVE_PORT=5001

# Frontend Configuration (for Next.js)
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
NEXT_PUBLIC_TEE_PUBKEY_URL=http://localhost:8000/tee-pubkey

# Security Configuration
TEE_PRIVATE_KEY_PATH=/app/tee_private_key.pem
ZK_VERIFICATION_KEY_PATH=/app/zk-circuit/verification_key.json

# Data Storage
DATA_DIR=/data
SUBMISSIONS_FILE=/data/submissions.json

# Model Configuration
MODEL_PATH=/app/model
MODEL_NAME=roberta-base

# ZK Circuit Configuration
CIRCUIT_PATH=/app/zk-circuit
CIRCUIT_NAME=pitch_eval

# Development Settings
DEBUG=false
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1

# Docker Configuration
COMPOSE_PROJECT_NAME=pitchguard
COMPOSE_FILE=docker-compose.yml
