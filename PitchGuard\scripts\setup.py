#!/usr/bin/env python3
"""
PitchGuard Setup Script
Initializes the project with keys, sample data, and ZK circuit compilation
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """Run a shell command"""
    print(f"Running: {cmd}")
    if isinstance(cmd, str):
        cmd = cmd.split()
    
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        sys.exit(1)
    
    return result

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    
    # Check Python
    try:
        import cryptography
        print("✓ Python cryptography library found")
    except ImportError:
        print("✗ Python cryptography library not found")
        print("Install with: pip install cryptography")
        return False
    
    # Check Node.js
    try:
        result = run_command("node --version", check=False)
        if result.returncode == 0:
            print(f"✓ Node.js found: {result.stdout.strip()}")
        else:
            print("✗ Node.js not found")
            return False
    except FileNotFoundError:
        print("✗ Node.js not found")
        return False
    
    # Check Docker
    try:
        result = run_command("docker --version", check=False)
        if result.returncode == 0:
            print(f"✓ Docker found: {result.stdout.strip()}")
        else:
            print("✗ Docker not found")
            return False
    except FileNotFoundError:
        print("✗ Docker not found")
        return False
    
    return True

def generate_keys():
    """Generate RSA key pairs"""
    print("\nGenerating RSA key pairs...")
    
    # Create keys directory
    keys_dir = Path("keys")
    keys_dir.mkdir(exist_ok=True)
    
    # Run key generation script
    script_dir = Path(__file__).parent
    generate_keys_script = script_dir / "generate_keys.py"
    
    run_command([sys.executable, str(generate_keys_script)])
    print("✓ RSA key pairs generated")

def setup_zk_circuit():
    """Set up the ZK circuit"""
    print("\nSetting up ZK circuit...")
    
    circuit_dir = Path("enclave/zk-circuit")
    
    # Check if circom is installed
    try:
        result = run_command("circom --version", check=False)
        if result.returncode != 0:
            print("✗ Circom not found. Please install circom first.")
            print("Visit: https://docs.circom.io/getting-started/installation/")
            return False
    except FileNotFoundError:
        print("✗ Circom not found. Please install circom first.")
        return False
    
    # Install snarkjs if not present
    try:
        result = run_command("snarkjs --version", check=False)
        if result.returncode != 0:
            print("Installing snarkjs...")
            run_command("npm install -g snarkjs")
    except FileNotFoundError:
        print("Installing snarkjs...")
        run_command("npm install -g snarkjs")
    
    # Make setup script executable and run it
    setup_script = circuit_dir / "setup_circuit.sh"
    if setup_script.exists():
        # On Windows, we'll run the commands manually
        if os.name == 'nt':
            print("Setting up circuit on Windows...")
            setup_circuit_windows(circuit_dir)
        else:
            run_command(f"chmod +x {setup_script}")
            run_command(str(setup_script), cwd=circuit_dir)
    else:
        print("Circuit setup script not found, creating minimal setup...")
        create_minimal_circuit_setup(circuit_dir)
    
    print("✓ ZK circuit setup complete")
    return True

def setup_circuit_windows(circuit_dir):
    """Set up circuit on Windows"""
    print("Compiling circuit...")
    run_command("circom pitch_eval.circom --r1cs --wasm --sym", cwd=circuit_dir)
    
    # Create a simple verification key for MVP
    vk_content = {
        "protocol": "groth16",
        "curve": "bn128",
        "nPublic": 3,
        "vk_alpha_1": ["0", "0", "0"],
        "vk_beta_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_gamma_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_delta_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_alphabeta_12": [],
        "IC": []
    }
    
    vk_file = circuit_dir / "verification_key.json"
    with open(vk_file, 'w') as f:
        json.dump(vk_content, f, indent=2)
    
    print("✓ Created mock verification key for MVP")

def create_minimal_circuit_setup(circuit_dir):
    """Create minimal circuit setup for MVP"""
    # Create a simple verification key
    vk_content = {
        "protocol": "groth16",
        "curve": "bn128",
        "nPublic": 3,
        "vk_alpha_1": ["0", "0", "0"],
        "vk_beta_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_gamma_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_delta_2": [["0", "0"], ["0", "0"], ["0", "0"]],
        "vk_alphabeta_12": [],
        "IC": []
    }
    
    vk_file = circuit_dir / "verification_key.json"
    with open(vk_file, 'w') as f:
        json.dump(vk_content, f, indent=2)
    
    print("✓ Created minimal circuit setup for MVP")

def create_sample_data():
    """Create sample pitch data for testing"""
    print("\nCreating sample data...")
    
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # Create sample pitch
    sample_pitch = """
# Revolutionary AI-Powered Fitness Platform

## Executive Summary
FitAI is a revolutionary fitness platform that uses artificial intelligence to create personalized workout plans and nutrition guidance. Our proprietary algorithm analyzes user data to provide real-time recommendations that adapt to individual progress and preferences.

## Problem Statement
The fitness industry lacks personalized, data-driven solutions. Current apps provide generic workouts that don't adapt to individual needs, leading to poor user engagement and limited results.

## Solution
FitAI leverages machine learning to create truly personalized fitness experiences:
- Real-time workout adaptation based on performance metrics
- Personalized nutrition plans using dietary preferences and goals
- AI-powered form correction using computer vision
- Social features to maintain motivation and accountability

## Market Opportunity
The global fitness app market is valued at $4.4 billion and growing at 14.7% CAGR. Our target market includes health-conscious individuals aged 25-45 who value technology-driven solutions.

## Business Model
- Freemium subscription model ($9.99/month premium)
- Corporate wellness partnerships
- Wearable device integrations and data partnerships

## Team
- CEO: Former Google AI researcher with 10 years ML experience
- CTO: Ex-Apple engineer specializing in mobile health applications
- Head of Fitness: Certified trainer with sports science PhD

## Financial Projections
- Year 1: $500K revenue, 10K users
- Year 2: $2M revenue, 50K users  
- Year 3: $8M revenue, 200K users

## Funding Request
Seeking $2M Series A to accelerate user acquisition and enhance AI capabilities.
"""
    
    sample_file = data_dir / "sample_pitch.txt"
    with open(sample_file, 'w') as f:
        f.write(sample_pitch.strip())
    
    print("✓ Sample pitch data created")

def main():
    """Main setup function"""
    print("PitchGuard Setup Script")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\nPlease install missing dependencies and run setup again.")
        sys.exit(1)
    
    # Generate keys
    generate_keys()
    
    # Set up ZK circuit
    if not setup_zk_circuit():
        print("Warning: ZK circuit setup failed. You can run it manually later.")
    
    # Create sample data
    create_sample_data()
    
    print("\n" + "=" * 50)
    print("✓ PitchGuard setup complete!")
    print("\nNext steps:")
    print("1. Run: docker-compose up --build")
    print("2. Open: http://localhost:3000 (frontend)")
    print("3. API docs: http://localhost:8000/docs (backend)")
    print("4. Test with sample pitch in data/sample_pitch.txt")

if __name__ == "__main__":
    main()
