pragma circom 2.0.0;

/*
 * PitchGuard ZK Circuit
 * Proves that scores were computed correctly from a pitch hash
 * without revealing the actual pitch content or intermediate computations
 */

template PitchEval() {
    // Private inputs
    signal private input pitch_hash;
    signal private input narrative_clarity;
    signal private input originality;
    signal private input team_strength;
    signal private input market_fit;
    signal private input overall_score;
    
    // Public inputs
    signal input model_commitment;
    
    // Public outputs
    signal output pitch_hash_out;
    signal output model_commitment_out;
    signal output scores_valid;
    
    // Constraints
    component eq1 = IsEqual();
    component eq2 = IsEqual();
    component eq3 = IsEqual();
    component eq4 = IsEqual();
    component eq5 = IsEqual();
    
    // Output the pitch hash (proves we know the original pitch)
    pitch_hash_out <== pitch_hash;
    model_commitment_out <== model_commitment;
    
    // Verify each score is in valid range (1-5)
    component range1 = Range(1, 5);
    component range2 = Range(1, 5);
    component range3 = Range(1, 5);
    component range4 = Range(1, 5);
    component range5 = Range(1, 5);
    
    range1.in <== narrative_clarity;
    range2.in <== originality;
    range3.in <== team_strength;
    range4.in <== market_fit;
    range5.in <== overall_score;
    
    // Verify scores are computed correctly from pitch hash
    // Simple scoring function: score = (pitch_hash % 5) + 1
    signal hash_mod_5;
    component mod = Mod(5);
    mod.dividend <== pitch_hash;
    hash_mod_5 <== mod.remainder;
    
    signal expected_score;
    expected_score <== hash_mod_5 + 1;
    
    // All scores should match the expected score (simplified for MVP)
    eq1.in[0] <== narrative_clarity;
    eq1.in[1] <== expected_score;
    
    eq2.in[0] <== originality;
    eq2.in[1] <== expected_score;
    
    eq3.in[0] <== team_strength;
    eq3.in[1] <== expected_score;
    
    eq4.in[0] <== market_fit;
    eq4.in[1] <== expected_score;
    
    // Verify overall score is average of individual scores
    signal sum_scores;
    sum_scores <== narrative_clarity + originality + team_strength + market_fit;
    
    signal expected_overall;
    component div = Div(4);
    div.dividend <== sum_scores;
    expected_overall <== div.quotient;
    
    eq5.in[0] <== overall_score;
    eq5.in[1] <== expected_overall;
    
    // All constraints must be satisfied
    signal all_valid;
    all_valid <== eq1.out * eq2.out * eq3.out * eq4.out * eq5.out;
    scores_valid <== all_valid;
}

// Helper templates
template IsEqual() {
    signal input in[2];
    signal output out;
    
    component eq = IsZero();
    eq.in <== in[0] - in[1];
    out <== eq.out;
}

template IsZero() {
    signal input in;
    signal output out;
    
    signal inv;
    inv <-- in != 0 ? 1/in : 0;
    out <== -in*inv + 1;
    in*out === 0;
}

template Range(min_val, max_val) {
    signal input in;
    
    component gte = GreaterEqThan(8);
    component lte = LessEqThan(8);
    
    gte.in[0] <== in;
    gte.in[1] <== min_val;
    
    lte.in[0] <== in;
    lte.in[1] <== max_val;
    
    gte.out === 1;
    lte.out === 1;
}

template GreaterEqThan(n) {
    assert(n <= 252);
    signal input in[2];
    signal output out;
    
    component lt = LessThan(n+1);
    lt.in[0] <== in[1];
    lt.in[1] <== in[0] + 1;
    out <== lt.out;
}

template LessEqThan(n) {
    assert(n <= 252);
    signal input in[2];
    signal output out;
    
    component lt = LessThan(n+1);
    lt.in[0] <== in[0];
    lt.in[1] <== in[1] + 1;
    out <== lt.out;
}

template LessThan(n) {
    assert(n <= 252);
    signal input in[2];
    signal output out;
    
    component n2b = Num2Bits(n);
    n2b.in <== in[0] + (1<<n) - in[1];
    out <== 1 - n2b.out[n];
}

template Num2Bits(n) {
    signal input in;
    signal output out[n];
    var lc1=0;
    
    var e2=1;
    for (var i = 0; i<n; i++) {
        out[i] <-- (in >> i) & 1;
        out[i] * (out[i] -1 ) === 0;
        lc1 += out[i] * e2;
        e2 = e2+e2;
    }
    
    lc1 === in;
}

template Mod(n) {
    signal input dividend;
    signal output quotient;
    signal output remainder;
    
    quotient <-- dividend \ n;
    remainder <-- dividend % n;
    
    dividend === quotient * n + remainder;
    
    component lt = LessThan(8);
    lt.in[0] <== remainder;
    lt.in[1] <== n;
    lt.out === 1;
}

template Div(n) {
    signal input dividend;
    signal output quotient;
    signal output remainder;
    
    quotient <-- dividend \ n;
    remainder <-- dividend % n;
    
    dividend === quotient * n + remainder;
}

component main = PitchEval();
