FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Install snarkjs globally for proof verification
RUN npm install -g snarkjs

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.backend.txt .
RUN pip install --no-cache-dir -r requirements.backend.txt

# Copy keys directory
COPY keys/ ./keys/

# Copy ZK verification key
COPY enclave/zk-circuit/verification_key.json ./zk-circuit/verification_key.json

# Copy backend application
COPY backend/main.py ./main.py

# Create data directory
RUN mkdir -p /data

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the backend service
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
