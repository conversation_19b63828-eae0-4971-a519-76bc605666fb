"use client"

import { useRef, useEffect, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Center } from "@react-three/drei"
import { useFrame } from "@react-three/fiber"
import { FileText, Shield, Brain, CheckCircle2 } from "lucide-react"
import type * as THREE from "three"

function TimelineIcon({ step, isActive }: { step: number; isActive: boolean }) {
  const meshRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (meshRef.current && isActive) {
      const time = state.clock.getElapsedTime()
      meshRef.current.rotation.y = time
      meshRef.current.position.y = Math.sin(time * 2) * 0.1
    }
  })

  const getGeometry = () => {
    switch (step) {
      case 0:
        return <boxGeometry args={[0.8, 1, 0.1]} />
      case 1:
        return <octahedronGeometry args={[0.6]} />
      case 2:
        return <sphereGeometry args={[0.6]} />
      case 3:
        return <torusGeometry args={[0.5, 0.2, 8, 16]} />
      default:
        return <boxGeometry args={[0.6, 0.6, 0.6]} />
    }
  }

  return (
    <mesh ref={meshRef}>
      {getGeometry()}
      <meshStandardMaterial
        color={isActive ? "#00FFD1" : "#A3A3FF"}
        emissive={isActive ? "#00FFD1" : "#000000"}
        emissiveIntensity={isActive ? 0.3 : 0}
        transparent
        opacity={isActive ? 1 : 0.6}
      />
    </mesh>
  )
}

export default function TimelineSection() {
  const [activeStep, setActiveStep] = useState(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const stepIndex = Number.parseInt(entry.target.getAttribute("data-step") || "0")
            setActiveStep(stepIndex)
          }
        })
      },
      { threshold: 0.5 },
    )

    // Add null check for sectionRef.current
    if (sectionRef.current) {
      const steps = sectionRef.current.querySelectorAll(".timeline-step")
      steps?.forEach((step) => observer.observe(step))
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  const timelineSteps = [
    {
      title: "Upload Your Pitch",
      description:
        "Securely upload your pitch deck through our encrypted interface. Your files are immediately protected with military-grade encryption.",
      icon: FileText,
    },
    {
      title: "Secure Enclave Processing",
      description:
        "Your pitch enters our secure enclave where it's processed in complete isolation from external systems and unauthorized access.",
      icon: Shield,
    },
    {
      title: "AI Analysis",
      description:
        "Our advanced AI models analyze your pitch structure, content quality, and market viability while maintaining complete privacy.",
      icon: Brain,
    },
    {
      title: "Zero-Knowledge Results",
      description:
        "Receive comprehensive feedback and scoring without exposing your original content. Your intellectual property remains completely protected.",
      icon: CheckCircle2,
    },
  ]

  return (
    <section id="timeline" ref={sectionRef} className="py-20 px-6 bg-gradient-to-b from-slate-900 to-indigo-950">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold font-orbitron text-white mb-6">How It Works</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Follow our secure 4-step process for privacy-first pitch evaluation
          </p>
        </div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-0.5 w-1 h-full bg-gradient-to-b from-cyan-400 via-blue-500 to-purple-600"></div>

          {timelineSteps.map((step, index) => (
            <div
              key={index}
              className={`timeline-step flex items-center mb-16 ${index % 2 === 0 ? "flex-row" : "flex-row-reverse"}`}
              data-step={index}
            >
              <div className={`w-5/12 ${index % 2 === 0 ? "text-right pr-8" : "text-left pl-8"}`}>
                <div
                  className={`transition-all duration-1000 ${
                    activeStep >= index ? "opacity-100 translate-y-0" : "opacity-50 translate-y-10"
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-4">
                    {index % 2 === 0 ? (
                      <>
                        <h3 className="text-2xl font-bold font-orbitron text-white">{step.title}</h3>
                        <step.icon className="w-6 h-6 text-cyan-400" />
                      </>
                    ) : (
                      <>
                        <step.icon className="w-6 h-6 text-cyan-400" />
                        <h3 className="text-2xl font-bold font-orbitron text-white">{step.title}</h3>
                      </>
                    )}
                  </div>
                  <p className="text-gray-300 leading-relaxed">{step.description}</p>
                </div>
              </div>

              <div className="w-2/12 flex justify-center">
                <div className="w-24 h-24 bg-indigo-950 rounded-full border-4 border-cyan-400 flex items-center justify-center relative z-10">
                  <Canvas camera={{ position: [0, 0, 2] }}>
                    <Environment preset="night" />
                    <ambientLight intensity={0.5} />
                    <pointLight position={[2, 2, 2]} intensity={1} color="#00FFD1" />
                    <Center>
                      <TimelineIcon step={index} isActive={activeStep >= index} />
                    </Center>
                  </Canvas>
                </div>
              </div>

              <div className="w-5/12"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
