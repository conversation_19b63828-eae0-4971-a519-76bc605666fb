#!/usr/bin/env python3
"""
Model setup script for PitchGuard enclave
Downloads and prepares a simple RoBERTa model for pitch evaluation
"""

import os
import json
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

def setup_model():
    """Set up the RoBERTa model for pitch evaluation"""
    print("Setting up RoBERTa model for PitchGuard...")
    
    model_name = "roberta-base"
    model_dir = os.path.dirname(__file__)
    
    try:
        # Download tokenizer and model
        print(f"Downloading {model_name}...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSequenceClassification.from_pretrained(model_name)
        
        # Save locally
        print("Saving model locally...")
        tokenizer.save_pretrained(model_dir)
        model.save_pretrained(model_dir)
        
        # Create model info file
        model_info = {
            "model_name": model_name,
            "model_type": "roberta-base",
            "task": "pitch-evaluation",
            "version": "1.0.0",
            "description": "Base RoBERTa model for pitch evaluation (MVP)",
            "input_format": "text",
            "output_format": "scores",
            "criteria": [
                "narrative_clarity",
                "originality", 
                "team_strength",
                "market_fit"
            ],
            "score_range": [1, 5]
        }
        
        with open(os.path.join(model_dir, "model_info.json"), 'w') as f:
            json.dump(model_info, f, indent=2)
        
        print("✓ Model setup complete!")
        print(f"Model saved to: {model_dir}")
        
        # Test the model
        print("\nTesting model...")
        test_text = "This is a revolutionary AI startup that will change the world."
        
        inputs = tokenizer(test_text, return_tensors="pt", truncation=True, max_length=512)
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        print(f"Test input: {test_text}")
        print(f"Model output shape: {logits.shape}")
        print("✓ Model test successful!")
        
    except Exception as e:
        print(f"Error setting up model: {e}")
        print("Creating minimal model placeholder...")
        
        # Create placeholder model info
        model_info = {
            "model_name": "mock-roberta",
            "model_type": "mock",
            "task": "pitch-evaluation",
            "version": "1.0.0-mock",
            "description": "Mock model for PitchGuard MVP",
            "input_format": "text",
            "output_format": "scores",
            "criteria": [
                "narrative_clarity",
                "originality",
                "team_strength", 
                "market_fit"
            ],
            "score_range": [1, 5],
            "note": "This is a mock model for MVP demonstration"
        }
        
        with open(os.path.join(model_dir, "model_info.json"), 'w') as f:
            json.dump(model_info, f, indent=2)
        
        print("✓ Mock model placeholder created")

if __name__ == "__main__":
    setup_model()
