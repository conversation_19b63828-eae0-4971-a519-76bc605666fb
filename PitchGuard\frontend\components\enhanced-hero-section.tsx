"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Html, useProgress } from "@react-three/drei"
import HeroSection from "@/components/hero-section"
import AnimatedGradientText from "@/components/ui/animated-gradient-text"
import Particles from "@/components/ui/particles"
import Meteors from "@/components/ui/meteors"
import { ArrowRight, Shield, Zap } from "lucide-react"

function Loader() {
  const { progress } = useProgress()
  return (
    <Html center>
      <div className="flex flex-col items-center justify-center">
        <div className="w-32 h-32 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mb-4"></div>
        <div className="text-white text-lg font-orbitron">Loading 3D Experience</div>
        <div className="text-cyan-400 text-sm">{progress.toFixed(0)}%</div>
      </div>
    </Html>
  )
}

export default function EnhancedHeroSection() {
  return (
    <section className="relative h-screen overflow-hidden">
      {/* Background Effects */}
      <Particles className="absolute inset-0" quantity={50} color="#00FFD1" />
      <Meteors number={30} />

      {/* 3D Canvas */}
      <Canvas camera={{ position: [0, 0, 5], fov: 75 }} className="absolute inset-0">
        <Suspense fallback={<Loader />}>
          <Environment preset="night" />
          <HeroSection />
        </Suspense>
      </Canvas>

      {/* Content Overlay */}
      <div className="absolute inset-0 flex flex-col justify-center items-center z-10 pointer-events-none">
        <div className="text-center max-w-5xl px-6">
          {/* Badge */}
          <div className="mb-8 pointer-events-auto">
            <AnimatedGradientText>
              <Shield className="w-4 h-4 mr-2" />
              <span className="text-white/90">Privacy-First AI Technology</span>
              <Zap className="w-4 h-4 ml-2" />
            </AnimatedGradientText>
          </div>

          {/* Main Heading */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold font-orbitron mb-8 leading-tight">
            <span className="bg-gradient-to-r from-white via-cyan-400 to-white bg-clip-text text-transparent">
              Encrypted
            </span>
            <br />
            <span className="bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
              Pitch Evaluation
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-400 via-cyan-400 to-white bg-clip-text text-transparent">
              in 3D
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl lg:text-3xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
            Experience privacy-first AI-driven pitch analysis through our{" "}
            <span className="text-cyan-400 font-semibold">revolutionary 3D interface</span>
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pointer-events-auto">
            <button className="group bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-bold py-4 px-8 rounded-2xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-400/25 flex items-center space-x-2">
              <span>Get Started</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>

            <button className="group bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white font-bold py-4 px-8 rounded-2xl text-lg transition-all duration-300 transform hover:scale-105">
              <span>Watch Demo</span>
            </button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-cyan-400 font-orbitron">10K+</div>
              <div className="text-sm text-gray-400 mt-1">Pitches Secured</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-cyan-400 font-orbitron">99.9%</div>
              <div className="text-sm text-gray-400 mt-1">Privacy Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-cyan-400 font-orbitron">50+</div>
              <div className="text-sm text-gray-400 mt-1">Countries</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  )
}
