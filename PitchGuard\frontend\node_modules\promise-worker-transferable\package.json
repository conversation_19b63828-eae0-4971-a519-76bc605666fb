{"name": "promise-worker-transferable", "version": "1.0.4", "description": "Communicate with a Web Worker using Promises, allowing transferList", "main": "index.js", "scripts": {"prepublish": "npm run build", "build": "rimraf dist && mkdirp dist && browserify -s PromiseWorker -p bundle-collapser/plugin . > dist/promise-worker.js && browserify -s registerPromiseWorker -p bundle-collapser/plugin register.js > dist/promise-worker.register.js && uglifyjs -mc < dist/promise-worker.js > dist/promise-worker.min.js && uglifyjs -mc < dist/promise-worker.register.js > dist/promise-worker.register.min.js", "build-test": "node bin/build-worker-bundles.js", "test": "npm run build-test && mocha test/test.js", "test-local": "npm run build-test && zuul --local 9000 --no-coverage test/test.js", "test-phantom": "npm run build-test && zuul --phantom --no-coverage test/test.js", "test-browser": "npm run build-test && zuul --no-coverage ./test/test.js", "lint": "jshint -c .jshintrc *.js", "istanbul-combine": "istanbul-combine -d coverage -r json coverage/coverage.json coverage/coverage-worker.json && mv coverage/coverage-final.json coverage/coverage.json && rimraf coverage/coverage-worker.json", "istanbul-check": "istanbul check-coverage --lines 100 --function 100 --statements 100 --branches 100", "test-for-coverage": "istanbul cover ./node_modules/.bin/_mocha ./test/test.js", "istanbul-report": "istanbul report lcov", "coverage": "COVERAGE=1 run-scripts build-test test-for-coverage istanbul-combine istanbul-report istanbul-check", "report-coverage": "npm run coverage && istanbul-coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/terikon/promise-worker-transferable.git"}, "keywords": ["web worker", "promise", "webworker", "worker"], "author": "<PERSON> Viskin <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/terikon/promise-worker-transferable/issues"}, "homepage": "https://github.com/terikon/promise-worker-transferable#readme", "devDependencies": {"assert": "^1.3.0", "browserify": "^13.0.1", "bundle-collapser": "^1.2.1", "denodeify": "^1.2.1", "glob-promise": "^1.0.6", "istanbul": "^0.4.2", "istanbul-combine": "^0.3.0", "istanbul-coveralls": "^1.0.3", "istanbulify": "^0.1.0", "jshint": "2.8.0", "mkdirp": "^0.5.1", "mocha": "^2.4.5", "phantomjs-prebuilt": "^2.1.5", "pseudo-worker": "^1.1.0", "rimraf": "^2.5.3", "run-scripts": "^0.4.0", "stream-to-promise": "^1.1.0", "uglify-js": "^2.7.0", "zuul": "^3.10.1", "zuul-localtunnel": "nolanlawson/zuul-localtunnel#https", "text-encoding": "^0.6.1"}, "dependencies": {"is-promise": "^2.1.0", "lie": "^3.0.2"}, "files": ["index.js", "register.js", "dist"]}