import React from 'react';
import { motion } from 'framer-motion';

interface FloatingElementsProps {
  count?: number;
  className?: string;
}

const FloatingElements: React.FC<FloatingElementsProps> = ({
  count = 8,
  className = '',
}) => {
  const elements = Array.from({ length: count }, (_, i) => {
    const size = Math.random() * 60 + 20;
    const delay = Math.random() * 5;
    const duration = Math.random() * 10 + 15;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    
    const shapes = ['circle', 'square', 'triangle', 'hexagon'];
    const shape = shapes[Math.floor(Math.random() * shapes.length)];
    
    const colors = [
      'rgba(59, 130, 246, 0.1)',
      'rgba(139, 92, 246, 0.1)',
      'rgba(6, 182, 212, 0.1)',
      'rgba(16, 185, 129, 0.1)',
      'rgba(245, 158, 11, 0.1)',
    ];
    const color = colors[Math.floor(Math.random() * colors.length)];

    return {
      id: i,
      size,
      delay,
      duration,
      x,
      y,
      shape,
      color,
    };
  });

  const getShapeStyles = (shape: string, size: number, color: string) => {
    const baseStyles = {
      width: size,
      height: size,
      background: color,
      backdropFilter: 'blur(1px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
    };

    switch (shape) {
      case 'circle':
        return {
          ...baseStyles,
          borderRadius: '50%',
        };
      case 'square':
        return {
          ...baseStyles,
          borderRadius: '8px',
        };
      case 'triangle':
        return {
          ...baseStyles,
          background: 'transparent',
          width: 0,
          height: 0,
          borderLeft: `${size / 2}px solid transparent`,
          borderRight: `${size / 2}px solid transparent`,
          borderBottom: `${size}px solid ${color}`,
        };
      case 'hexagon':
        return {
          ...baseStyles,
          clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
        };
      default:
        return baseStyles;
    }
  };

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            ...getShapeStyles(element.shape, element.size, element.color),
          }}
          animate={{
            y: [-20, 20, -20],
            x: [-10, 10, -10],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

export default FloatingElements;
