#!/usr/bin/env python3
"""
Key generation script for PitchGuard
Generates RSA key pairs for T<PERSON> and founder (demo purposes)
"""

import os
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend

def generate_rsa_keypair(key_size=2048):
    """Generate RSA key pair"""
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=key_size,
        backend=default_backend()
    )
    public_key = private_key.public_key()
    return private_key, public_key

def save_private_key(private_key, filepath):
    """Save private key to PEM file"""
    pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    with open(filepath, 'wb') as f:
        f.write(pem)

def save_public_key(public_key, filepath):
    """Save public key to PEM file"""
    pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    with open(filepath, 'wb') as f:
        f.write(pem)

def main():
    """Generate all required keys"""
    print("Generating RSA key pairs for PitchGuard...")
    
    # Create keys directory
    keys_dir = os.path.join(os.path.dirname(__file__), '..', 'keys')
    os.makedirs(keys_dir, exist_ok=True)
    
    # Generate TEE key pair
    print("Generating TEE key pair...")
    tee_private, tee_public = generate_rsa_keypair()
    save_private_key(tee_private, os.path.join(keys_dir, 'tee_private_key.pem'))
    save_public_key(tee_public, os.path.join(keys_dir, 'tee_public_key.pem'))
    
    # Generate founder key pair (for demo)
    print("Generating founder key pair...")
    founder_private, founder_public = generate_rsa_keypair()
    save_private_key(founder_private, os.path.join(keys_dir, 'founder_private_key.pem'))
    save_public_key(founder_public, os.path.join(keys_dir, 'founder_public_key.pem'))
    
    print("Key generation complete!")
    print(f"Keys saved to: {keys_dir}")
    print("- tee_private_key.pem")
    print("- tee_public_key.pem") 
    print("- founder_private_key.pem")
    print("- founder_public_key.pem")

if __name__ == "__main__":
    main()
