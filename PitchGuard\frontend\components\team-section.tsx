"use client"

import { useRef, useEffect, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { Environment, Sphere } from "@react-three/drei"
import { useFrame } from "@react-three/fiber"
import type * as THREE from "three"

function RotatingGlobe() {
  const globeRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (globeRef.current) {
      globeRef.current.rotation.y = state.clock.getElapsedTime() * 0.1
    }
  })

  return (
    <Sphere ref={globeRef} args={[2, 32, 32]}>
      <meshStandardMaterial color="#1C1F4A" wireframe transparent opacity={0.2} />
    </Sphere>
  )
}

function TeamCard({
  name,
  role,
  quote,
  image,
  isVisible,
}: {
  name: string
  role: string
  quote: string
  image: string
  isVisible: boolean
}) {
  return (
    <div
      className={`bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-cyan-400/20 transition-all duration-1000 hover:transform hover:scale-105 hover:border-cyan-400/40 ${
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-20"
      }`}
    >
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
          <span className="text-2xl font-bold text-white">{name.charAt(0)}</span>
        </div>
        <div>
          <h3 className="text-xl font-bold text-white font-orbitron">{name}</h3>
          <p className="text-cyan-400">{role}</p>
        </div>
      </div>
      <blockquote className="text-gray-300 italic leading-relaxed">"{quote}"</blockquote>
    </div>
  )
}

export default function TeamSection() {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
          }
        })
      },
      { threshold: 0.3 },
    )

    // Add null check for sectionRef.current
    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  const teamMembers = [
    {
      name: "Alex Chen",
      role: "CEO & Co-founder",
      quote:
        "Privacy shouldn't be a luxury in pitch evaluation. We're building the future where entrepreneurs can get feedback without compromising their ideas.",
      image: "/placeholder.svg?height=64&width=64",
    },
    {
      name: "Sarah Rodriguez",
      role: "CTO & Co-founder",
      quote:
        "Our zero-knowledge architecture ensures that even we can't see your pitch content, while still providing world-class AI analysis.",
      image: "/placeholder.svg?height=64&width=64",
    },
    {
      name: "Dr. Michael Kim",
      role: "Head of AI Research",
      quote:
        "We've developed proprietary algorithms that can analyze pitch quality while operating in secure enclaves, maintaining complete data privacy.",
      image: "/placeholder.svg?height=64&width=64",
    },
    {
      name: "Emma Thompson",
      role: "Head of Security",
      quote:
        "Every line of code is audited for security. We use military-grade encryption and zero-trust architecture to protect your intellectual property.",
      image: "/placeholder.svg?height=64&width=64",
    },
  ]

  return (
    <section
      id="team"
      ref={sectionRef}
      className="py-20 px-6 bg-gradient-to-b from-indigo-950 to-slate-900 relative overflow-hidden"
    >
      {/* Background 3D Globe */}
      <div className="absolute inset-0 opacity-30">
        <Canvas camera={{ position: [0, 0, 8] }}>
          <Environment preset="night" />
          <ambientLight intensity={0.2} />
          <RotatingGlobe />
        </Canvas>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold font-orbitron text-white mb-6">Meet Our Team</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            World-class experts in AI, security, and privacy-preserving technologies
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {teamMembers.map((member, index) => (
            <TeamCard
              key={index}
              name={member.name}
              role={member.role}
              quote={member.quote}
              image={member.image}
              isVisible={isVisible}
            />
          ))}
        </div>

        <div className="text-center mt-16">
          <div
            className={`transition-all duration-1000 delay-500 ${
              isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
            }`}
          >
            <h3 className="text-2xl font-bold text-white mb-4 font-orbitron">Trusted Globally</h3>
            <p className="text-gray-300 mb-8">
              Join thousands of entrepreneurs who trust PitchGuard with their most valuable ideas
            </p>
            <div className="flex justify-center space-x-8 text-cyan-400">
              <div className="text-center">
                <div className="text-3xl font-bold">10K+</div>
                <div className="text-sm text-gray-400">Pitches Analyzed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">50+</div>
                <div className="text-sm text-gray-400">Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">99.9%</div>
                <div className="text-sm text-gray-400">Uptime</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
