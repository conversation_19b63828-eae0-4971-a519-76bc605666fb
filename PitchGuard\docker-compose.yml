version: '3.8'

services:
  enclave:
    build:
      context: .
      dockerfile: docker/Dockerfile.enclave
    container_name: pitchguard-enclave
    volumes:
      - ./data:/data
      - ./keys:/app/keys:ro
      - ./enclave:/app/enclave:ro
    ports:
      - "5001:5001"
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - pitchguard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: pitchguard-backend
    volumes:
      - ./data:/data
      - ./keys:/app/keys:ro
      - ./enclave/zk-circuit:/app/zk-circuit:ro
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - ENCLAVE_URL=http://enclave:5001
    depends_on:
      enclave:
        condition: service_healthy
    networks:
      - pitchguard-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pitchguard-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
      - NEXT_PUBLIC_TEE_PUBKEY_URL=http://localhost:8000/tee-pubkey
    depends_on:
      - backend
    networks:
      - pitchguard-network
    restart: unless-stopped

networks:
  pitchguard-network:
    driver: bridge

volumes:
  pitchguard-data:
    driver: local
