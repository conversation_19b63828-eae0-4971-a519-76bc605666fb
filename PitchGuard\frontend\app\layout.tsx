import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'PitchGuard - Secure Pitch Evaluation',
  description: 'Encrypted pitch submission with TEE-enforced NLP scoring and zero-knowledge proof verification',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gray-50">
          <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-4">
                <div className="flex items-center">
                  <h1 className="text-2xl font-bold text-gray-900">PitchGuard</h1>
                  <span className="ml-2 px-2 py-1 text-xs bg-primary-100 text-primary-600 rounded-full">
                    MVP
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  Secure • Private • Verifiable
                </div>
              </div>
            </div>
          </header>
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {children}
          </main>
          <footer className="bg-white border-t mt-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="text-center text-sm text-gray-500">
                PitchGuard MVP - Demonstrating encrypted pitch evaluation with TEE and ZK proofs
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}
