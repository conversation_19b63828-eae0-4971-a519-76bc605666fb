"use client"

import { useState, useEffect } from "react"
import { Menu, X, Shield, Github, Twitter, Linkedin } from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import { Dock, DockIcon } from "@/components/ui/dock"

export default function EnhancedNavigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    if (typeof window !== "undefined") {
      window.addEventListener("scroll", handleScroll)
      return () => window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  const navItems = [
    { name: "Features", href: "#features" },
    { name: "How It Works", href: "#timeline" },
    { name: "Team", href: "#team" },
    { name: "<PERSON>", href: "#contact" },
  ]

  const socialItems = [
    { icon: Github, href: "#", label: "GitHub" },
    { icon: Twitter, href: "#", label: "Twitter" },
    { icon: Linkedin, href: "#", label: "LinkedIn" },
  ]

  return (
    <>
      {/* Main Navigation */}
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled ? "bg-indigo-950/80 backdrop-blur-md border-b border-white/10" : "bg-transparent"
        }`}
      >
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-3 group cursor-pointer">
              <div className="relative">
                <Shield className="w-8 h-8 text-cyan-400 group-hover:text-cyan-300 transition-colors" />
                <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-lg group-hover:bg-cyan-400/30 transition-all"></div>
              </div>
              <span className="text-2xl font-bold font-orbitron text-white group-hover:text-cyan-100 transition-colors">
                PitchGuard
              </span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-gray-300 hover:text-cyan-400 transition-all duration-300 relative group py-2"
                >
                  {item.name}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 transition-all duration-300 group-hover:w-full"></span>
                </a>
              ))}
              <div className="flex items-center space-x-4">
                <ThemeToggle />
                <button className="bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300 transform hover:scale-105">
                  Sign In
                </button>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-white p-2 rounded-lg hover:bg-white/10 transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t border-gray-700/50">
              <div className="flex flex-col space-y-4 pt-4">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-gray-300 hover:text-cyan-400 transition-colors duration-200 py-2"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                ))}
                <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                  <ThemeToggle />
                  <button className="bg-gradient-to-r from-cyan-400 to-blue-500 text-white font-semibold py-2 px-6 rounded-xl">
                    Sign In
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Floating Social Dock */}
      <div className="fixed bottom-8 right-8 z-40 hidden lg:block">
        <Dock direction="middle" className="bg-black/20 border-white/10" magnification={50} distance={100}>
          {socialItems.map((item) => (
            <DockIcon key={item.label} className="bg-white/10 hover:bg-cyan-400/20 transition-colors">
              <a href={item.href} aria-label={item.label} className="flex items-center justify-center w-full h-full">
                <item.icon className="w-5 h-5 text-white hover:text-cyan-400 transition-colors" />
              </a>
            </DockIcon>
          ))}
        </Dock>
      </div>
    </>
  )
}
